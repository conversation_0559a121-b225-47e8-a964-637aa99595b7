########### AGGREGATED COMPONENTS AND DEPENDENCIES FOR THE MULTI CONFIG #####################
#############################################################################################

set(backward-cpp_COMPONENT_NAMES "")
if(DEFINED backward-cpp_FIND_DEPENDENCY_NAMES)
  list(APPEND backward-cpp_FIND_DEPENDENCY_NAMES )
  list(REMOVE_DUPLICATES backward-cpp_FIND_DEPENDENCY_NAMES)
else()
  set(backward-cpp_FIND_DEPENDENCY_NAMES )
endif()

########### VARIABLES #######################################################################
#############################################################################################
set(backward-cpp_PACKAGE_FOLDER_RELEASE "C:/Users/<USER>/.conan2/p/backwb2c79bbbe4818/p")
set(backward-cpp_BUILD_MODULES_PATHS_RELEASE )


set(backward-cpp_INCLUDE_DIRS_RELEASE "${backward-cpp_PACKAGE_FOLDER_RELEASE}/include")
set(backward-cpp_RES_DIRS_RELEASE )
set(backward-cpp_DEFINITIONS_RELEASE "-DBACKWARD_HAS_UNWIND=1"
			"-DBACKWARD_HAS_LIBUNWIND=0"
			"-DBACKWARD_HAS_BACKTRACE=0"
			"-DBACKWARD_HAS_BACKTRACE_SYMBOL=0"
			"-DBACKWARD_HAS_DW=0"
			"-DBACKWARD_HAS_BFD=0"
			"-DBACKWARD_HAS_DWARF=0"
			"-DBACKWARD_HAS_PDB_SYMBOL=1")
set(backward-cpp_SHARED_LINK_FLAGS_RELEASE )
set(backward-cpp_EXE_LINK_FLAGS_RELEASE )
set(backward-cpp_OBJECTS_RELEASE )
set(backward-cpp_COMPILE_DEFINITIONS_RELEASE "BACKWARD_HAS_UNWIND=1"
			"BACKWARD_HAS_LIBUNWIND=0"
			"BACKWARD_HAS_BACKTRACE=0"
			"BACKWARD_HAS_BACKTRACE_SYMBOL=0"
			"BACKWARD_HAS_DW=0"
			"BACKWARD_HAS_BFD=0"
			"BACKWARD_HAS_DWARF=0"
			"BACKWARD_HAS_PDB_SYMBOL=1")
set(backward-cpp_COMPILE_OPTIONS_C_RELEASE )
set(backward-cpp_COMPILE_OPTIONS_CXX_RELEASE )
set(backward-cpp_LIB_DIRS_RELEASE "${backward-cpp_PACKAGE_FOLDER_RELEASE}/lib")
set(backward-cpp_BIN_DIRS_RELEASE )
set(backward-cpp_LIBRARY_TYPE_RELEASE STATIC)
set(backward-cpp_IS_HOST_WINDOWS_RELEASE 1)
set(backward-cpp_LIBS_RELEASE backward)
set(backward-cpp_SYSTEM_LIBS_RELEASE psapi dbghelp)
set(backward-cpp_FRAMEWORK_DIRS_RELEASE )
set(backward-cpp_FRAMEWORKS_RELEASE )
set(backward-cpp_BUILD_DIRS_RELEASE )
set(backward-cpp_NO_SONAME_MODE_RELEASE FALSE)


# COMPOUND VARIABLES
set(backward-cpp_COMPILE_OPTIONS_RELEASE
    "$<$<COMPILE_LANGUAGE:CXX>:${backward-cpp_COMPILE_OPTIONS_CXX_RELEASE}>"
    "$<$<COMPILE_LANGUAGE:C>:${backward-cpp_COMPILE_OPTIONS_C_RELEASE}>")
set(backward-cpp_LINKER_FLAGS_RELEASE
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,SHARED_LIBRARY>:${backward-cpp_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,MODULE_LIBRARY>:${backward-cpp_SHARED_LINK_FLAGS_RELEASE}>"
    "$<$<STREQUAL:$<TARGET_PROPERTY:TYPE>,EXECUTABLE>:${backward-cpp_EXE_LINK_FLAGS_RELEASE}>")


set(backward-cpp_COMPONENTS_RELEASE )