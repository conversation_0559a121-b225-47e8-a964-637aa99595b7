
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:8 (project)"
    message: |
      The system is: Windows - 6.2.9200 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:8 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: /MP16
      Id flags:  
      
      The output was:
      0
      Versión de MSBuild 17.13.19+0d9f5a35a para .NET Framework
      Compilación iniciada a las 26/05/2025 12:53:21.
      
      Proyecto "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj" en nodo 1 (destinos predeterminados).
      PrepareForBuild:
        Creando directorio "Debug\\".
        La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
        Creando directorio "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Se creará "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
        Aplicando tarea Touch a "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Se eliminará el archivo "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Aplicando tarea Touch a "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Compilación del proyecto terminada "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\3.31.5\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (destinos predeterminados).
      
      Compilación correcta.
          0 Advertencia(s)
          0 Errores
      
      Tiempo transcurrido 00:00:01.26
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/repos/HVIS/build/CMakeFiles/3.31.5/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:8 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-0524oe"
      binary: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-0524oe"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/MP16 /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/repos/HVIS/build"
      CMAKE_MSVC_RUNTIME_LIBRARY: "$<$<CONFIG:Release>:MultiThreadedDLL>"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-0524oe'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_9b4f0.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versión de MSBuild 17.13.19+0d9f5a35a para .NET Framework
        Compilación iniciada a las 26/05/2025 12:53:23.
        
        Proyecto "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0524oe\\cmTC_9b4f0.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_9b4f0.dir\\Debug\\".
          La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
          Creando directorio "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0524oe\\Debug\\".
          Creando directorio "cmTC_9b4f0.dir\\Debug\\cmTC_9b4f0.tlog\\".
        InitializeBuildStatus:
          Se creará "cmTC_9b4f0.dir\\Debug\\cmTC_9b4f0.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_9b4f0.dir\\Debug\\cmTC_9b4f0.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++14 /Fo"cmTC_9b4f0.dir\\Debug\\\\" /Fd"cmTC_9b4f0.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\mingw64\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Compilador de optimización de C/C++ de Microsoft (R) versión 19.43.34810 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++14 /Fo"cmTC_9b4f0.dir\\Debug\\\\" /Fd"cmTC_9b4f0.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\mingw64\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0524oe\\Debug\\cmTC_9b4f0.exe" /INCREMENTAL /ILK:"cmTC_9b4f0.dir\\Debug\\cmTC_9b4f0.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-0524oe/Debug/cmTC_9b4f0.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-0524oe/Debug/cmTC_9b4f0.lib" /MACHINE:X64  /machine:x64 cmTC_9b4f0.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_9b4f0.vcxproj -> C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0524oe\\Debug\\cmTC_9b4f0.exe
        FinalizeBuildStatus:
          Se eliminará el archivo "cmTC_9b4f0.dir\\Debug\\cmTC_9b4f0.tlog\\unsuccessfulbuild".
          Aplicando tarea Touch a "cmTC_9b4f0.dir\\Debug\\cmTC_9b4f0.tlog\\cmTC_9b4f0.lastbuildstate".
        Compilación del proyecto terminada "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0524oe\\cmTC_9b4f0.vcxproj" (destinos predeterminados).
        
        Compilación correcta.
            0 Advertencia(s)
            0 Errores
        
        Tiempo transcurrido 00:00:01.39
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:8 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/mingw64/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:8 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.43.34808/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.43.34810.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/mingw64/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/mingw64/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/mingw64/share/cmake-3.31/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "C:/mingw64/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:162 (include)"
      - "HVIS_OnlineInterface/CMakeLists.txt:23 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-v4srck"
      binary: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-v4srck"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/MP16 /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/repos/HVIS/build;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
      CMAKE_MSVC_RUNTIME_LIBRARY: "$<$<CONFIG:Release>:MultiThreadedDLL>"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-v4srck'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_1cc28.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versión de MSBuild 17.13.19+0d9f5a35a para .NET Framework
        Compilación iniciada a las 26/05/2025 12:53:25.
        
        Proyecto "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\cmTC_1cc28.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_1cc28.dir\\Debug\\".
          La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
          Creando directorio "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\Debug\\".
          Creando directorio "cmTC_1cc28.dir\\Debug\\cmTC_1cc28.tlog\\".
        InitializeBuildStatus:
          Se creará "cmTC_1cc28.dir\\Debug\\cmTC_1cc28.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_1cc28.dir\\Debug\\cmTC_1cc28.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_1cc28.dir\\Debug\\\\" /Fd"cmTC_1cc28.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\src.cxx"
          Compilador de optimización de C/C++ de Microsoft (R) versión 19.43.34810 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_1cc28.dir\\Debug\\\\" /Fd"cmTC_1cc28.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\src.cxx"
          src.cxx
        C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\src.cxx(1,10): error C1083: No se puede abrir el archivo incluir: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\cmTC_1cc28.vcxproj]
        Compilación del proyecto terminada "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\cmTC_1cc28.vcxproj" (destinos predeterminados) -- ERROR.
        
        ERROR al compilar.
        
        "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\cmTC_1cc28.vcxproj" (destino predeterminado) (1) ->
        (ClCompile destino) -> 
          C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\src.cxx(1,10): error C1083: No se puede abrir el archivo incluir: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v4srck\\cmTC_1cc28.vcxproj]
        
            0 Advertencia(s)
            1 Errores
        
        Tiempo transcurrido 00:00:00.76
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/mingw64/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/mingw64/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/mingw64/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:162 (include)"
      - "HVIS_OnlineInterface/CMakeLists.txt:23 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-z2917k"
      binary: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-z2917k"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/MP16 /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/repos/HVIS/build;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
      CMAKE_MSVC_RUNTIME_LIBRARY: "$<$<CONFIG:Release>:MultiThreadedDLL>"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-z2917k'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c17d6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versión de MSBuild 17.13.19+0d9f5a35a para .NET Framework
        Compilación iniciada a las 26/05/2025 12:53:26.
        
        Proyecto "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z2917k\\cmTC_c17d6.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_c17d6.dir\\Debug\\".
          La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
          Creando directorio "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z2917k\\Debug\\".
          Creando directorio "cmTC_c17d6.dir\\Debug\\cmTC_c17d6.tlog\\".
        InitializeBuildStatus:
          Se creará "cmTC_c17d6.dir\\Debug\\cmTC_c17d6.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_c17d6.dir\\Debug\\cmTC_c17d6.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_c17d6.dir\\Debug\\\\" /Fd"cmTC_c17d6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z2917k\\CheckFunctionExists.cxx"
          Compilador de optimización de C/C++ de Microsoft (R) versión 19.43.34810 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_c17d6.dir\\Debug\\\\" /Fd"cmTC_c17d6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z2917k\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z2917k\\Debug\\cmTC_c17d6.exe" /INCREMENTAL /ILK:"cmTC_c17d6.dir\\Debug\\cmTC_c17d6.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-z2917k/Debug/cmTC_c17d6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-z2917k/Debug/cmTC_c17d6.lib" /MACHINE:X64  /machine:x64 cmTC_c17d6.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: no se puede abrir el archivo 'pthreads.lib' [C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z2917k\\cmTC_c17d6.vcxproj]
        Compilación del proyecto terminada "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z2917k\\cmTC_c17d6.vcxproj" (destinos predeterminados) -- ERROR.
        
        ERROR al compilar.
        
        "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z2917k\\cmTC_c17d6.vcxproj" (destino predeterminado) (1) ->
        (Link destino) -> 
          LINK : fatal error LNK1104: no se puede abrir el archivo 'pthreads.lib' [C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z2917k\\cmTC_c17d6.vcxproj]
        
            0 Advertencia(s)
            1 Errores
        
        Tiempo transcurrido 00:00:00.94
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/mingw64/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/mingw64/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/mingw64/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:162 (include)"
      - "HVIS_OnlineInterface/CMakeLists.txt:23 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-ql8ngw"
      binary: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-ql8ngw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/MP16 /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/repos/HVIS/build;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
      CMAKE_MSVC_RUNTIME_LIBRARY: "$<$<CONFIG:Release>:MultiThreadedDLL>"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-ql8ngw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_47da3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versión de MSBuild 17.13.19+0d9f5a35a para .NET Framework
        Compilación iniciada a las 26/05/2025 12:53:28.
        
        Proyecto "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ql8ngw\\cmTC_47da3.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_47da3.dir\\Debug\\".
          La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
          Creando directorio "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ql8ngw\\Debug\\".
          Creando directorio "cmTC_47da3.dir\\Debug\\cmTC_47da3.tlog\\".
        InitializeBuildStatus:
          Se creará "cmTC_47da3.dir\\Debug\\cmTC_47da3.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_47da3.dir\\Debug\\cmTC_47da3.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_47da3.dir\\Debug\\\\" /Fd"cmTC_47da3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ql8ngw\\CheckFunctionExists.cxx"
          Compilador de optimización de C/C++ de Microsoft (R) versión 19.43.34810 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_47da3.dir\\Debug\\\\" /Fd"cmTC_47da3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ql8ngw\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ql8ngw\\Debug\\cmTC_47da3.exe" /INCREMENTAL /ILK:"cmTC_47da3.dir\\Debug\\cmTC_47da3.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-ql8ngw/Debug/cmTC_47da3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-ql8ngw/Debug/cmTC_47da3.lib" /MACHINE:X64  /machine:x64 cmTC_47da3.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: no se puede abrir el archivo 'pthread.lib' [C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ql8ngw\\cmTC_47da3.vcxproj]
        Compilación del proyecto terminada "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ql8ngw\\cmTC_47da3.vcxproj" (destinos predeterminados) -- ERROR.
        
        ERROR al compilar.
        
        "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ql8ngw\\cmTC_47da3.vcxproj" (destino predeterminado) (1) ->
        (Link destino) -> 
          LINK : fatal error LNK1104: no se puede abrir el archivo 'pthread.lib' [C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ql8ngw\\cmTC_47da3.vcxproj]
        
            0 Advertencia(s)
            1 Errores
        
        Tiempo transcurrido 00:00:00.96
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/mingw64/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/mingw64/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "C:/mingw64/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/Qt6Config.cmake:212 (find_package)"
      - "HVIS_OnlineInterface/CMakeLists.txt:23 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-7mphdr"
      binary: "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-7mphdr"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/MP16 /DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/repos/HVIS/build;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6/3rdparty/kwin"
      CMAKE_MSVC_RUNTIME_LIBRARY: "$<$<CONFIG:Release>:MultiThreadedDLL>"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-7mphdr'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_12467.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        Versión de MSBuild 17.13.19+0d9f5a35a para .NET Framework
        Compilación iniciada a las 26/05/2025 12:53:29.
        
        Proyecto "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7mphdr\\cmTC_12467.vcxproj" en nodo 1 (destinos predeterminados).
        PrepareForBuild:
          Creando directorio "cmTC_12467.dir\\Debug\\".
          La salida estructurada está habilitada. El formato del diagnóstico del compilador reflejará la jerarquía de errores. Consulte https://aka.ms/cpp/structured-output para obtener más detalles.
          Creando directorio "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7mphdr\\Debug\\".
          Creando directorio "cmTC_12467.dir\\Debug\\cmTC_12467.tlog\\".
        InitializeBuildStatus:
          Se creará "cmTC_12467.dir\\Debug\\cmTC_12467.tlog\\unsuccessfulbuild" porque se especificó "AlwaysCreate".
          Aplicando tarea Touch a "cmTC_12467.dir\\Debug\\cmTC_12467.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_12467.dir\\Debug\\\\" /Fd"cmTC_12467.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7mphdr\\src.cxx"
          Compilador de optimización de C/C++ de Microsoft (R) versión 19.43.34810 para x64
          (C) Microsoft Corporation. Todos los derechos reservados.
          cl /c /Zi /W1 /WX- /diagnostics:column /MP16 /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_12467.dir\\Debug\\\\" /Fd"cmTC_12467.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7mphdr\\src.cxx"
          src.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.43.34808\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7mphdr\\Debug\\cmTC_12467.exe" /INCREMENTAL /ILK:"cmTC_12467.dir\\Debug\\cmTC_12467.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-7mphdr/Debug/cmTC_12467.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/repos/HVIS/build/CMakeFiles/CMakeScratch/TryCompile-7mphdr/Debug/cmTC_12467.lib" /MACHINE:X64  /machine:x64 cmTC_12467.dir\\Debug\\src.obj
          cmTC_12467.vcxproj -> C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7mphdr\\Debug\\cmTC_12467.exe
        FinalizeBuildStatus:
          Se eliminará el archivo "cmTC_12467.dir\\Debug\\cmTC_12467.tlog\\unsuccessfulbuild".
          Aplicando tarea Touch a "cmTC_12467.dir\\Debug\\cmTC_12467.tlog\\cmTC_12467.lastbuildstate".
        Compilación del proyecto terminada "C:\\Users\\<USER>\\repos\\HVIS\\build\\CMakeFiles\\CMakeScratch\\TryCompile-7mphdr\\cmTC_12467.vcxproj" (destinos predeterminados).
        
        Compilación correcta.
            0 Advertencia(s)
            0 Errores
        
        Tiempo transcurrido 00:00:01.45
        
      exitCode: 0
...
