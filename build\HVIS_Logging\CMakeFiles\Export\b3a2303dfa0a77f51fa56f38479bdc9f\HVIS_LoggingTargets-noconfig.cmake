#----------------------------------------------------------------
# Generated CMake target import file.
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "HVIS::Logging" for configuration ""
set_property(TARGET HVIS::Logging APPEND PROPERTY IMPORTED_CONFIGURATIONS NOCONFIG)
set_target_properties(HVIS::Logging PROPERTIES
  IMPORTED_LINK_INTERFACE_LANGUAGES_NOCONFIG "CXX"
  IMPORTED_LOCATION_NOCONFIG "${_IMPORT_PREFIX}/lib/libhvis_logging.a"
  )

list(APPEND _cmake_import_check_targets HVIS::Logging )
list(APPEND _cmake_import_check_files_for_HVIS::Logging "${_IMPORT_PREFIX}/lib/libhvis_logging.a" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
