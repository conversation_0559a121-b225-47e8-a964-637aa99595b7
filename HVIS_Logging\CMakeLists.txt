cmake_minimum_required(VERSION 3.16) 
project(HVIS_Logging VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

include(FetchContent) 
FetchContent_Declare( spdlog GIT_REPOSITORY https://github.com/gabime/spdlog.git GIT_TAG v1.14.1 ) 
FetchContent_MakeAvailable(spdlog)

FetchContent_Declare( backward GIT_REPOSITORY https://github.com/bombela/backward-cpp.git GIT_TAG v1.6 )
FetchContent_MakeAvailable(backward)

# Define the library

add_library(HVIS_Logging src/HVIS_Logging.cpp) 
target_include_directories(HVIS_Logging PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/include ) 
target_link_libraries(HVIS_Logging PUBLIC spdlog::spdlog)

# Install the library

install(TARGETS HVIS_Logging LIBRARY DESTINATION lib ARCHIVE DESTINATION lib)
install(DIRECTORY include/ DESTINATION include)

# Enable stacktrace support for backward-cpp

# add_definitions(-DBACKWARD_HAS_DW=1) # Use libdw for stack traces (Linux)

# Adjust for other platforms if needed (e.g., -DBACKWARD_HAS_BFD=1)