cmake_minimum_required(VERSION 3.16)

# Set policy to avoid duplicate target errors
if(POLICY CMP0002)
    cmake_policy(SET CMP0002 NEW)
endif()

project(HVIS_Logging VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find packages using Conan
find_package(spdlog REQUIRED)
find_package(Backward REQUIRED)

# Define the library
add_library(HVIS_Logging STATIC src/HVIS_Logging.cpp)
target_include_directories(HVIS_Logging PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)
target_link_libraries(HVIS_Logging PUBLIC spdlog::spdlog)

# Set target properties to avoid conflicts
set_target_properties(HVIS_Logging PROPERTIES
    OUTPUT_NAME "hvis_logging"
    EXPORT_NAME "Logging"
)

# Create alias for consistent naming
add_library(HVIS::Logging ALIAS HVIS_Logging)

# Install the library
install(TARGETS HVIS_Logging
    EXPORT HVIS_LoggingTargets
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
    INCLUDES DESTINATION include
)

install(DIRECTORY include/ DESTINATION include)

# Export targets for other CMake projects
install(EXPORT HVIS_LoggingTargets
    FILE HVIS_LoggingTargets.cmake
    NAMESPACE HVIS::
    DESTINATION lib/cmake/HVIS_Logging
)

# Create config file
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/HVIS_LoggingConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

configure_package_config_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake/HVIS_LoggingConfig.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/HVIS_LoggingConfig.cmake"
    INSTALL_DESTINATION lib/cmake/HVIS_Logging
)

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/HVIS_LoggingConfig.cmake"
    "${CMAKE_CURRENT_BINARY_DIR}/HVIS_LoggingConfigVersion.cmake"
    DESTINATION lib/cmake/HVIS_Logging
)

# Enable stacktrace support for backward-cpp
# add_definitions(-DBACKWARD_HAS_DW=1) # Use libdw for stack traces (Linux)
# Adjust for other platforms if needed (e.g., -DBACKWARD_HAS_BFD=1)

# Optional: Build tests
option(BUILD_TESTS "Build test programs" OFF)
if(BUILD_TESTS)
    add_executable(test_logging tests/test_logging.cpp)
    target_link_libraries(test_logging PRIVATE HVIS::Logging)

    # Create test logs directory
    file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/test_logs)
endif()