#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <memory>

#include "EuresysFrameGrabber.h"

// Forward declaration to avoid including the full header
namespace spdlog {
    class logger;
}

QT_BEGIN_NAMESPACE
namespace Ui {
class MainWindow;
}
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void on_previousPage_clicked();
    void on_nextPage_clicked();
    void on_playStandbyButton_clicked();

    // TODO: define how the callbacks are being called
    void on_callbackFrameReceived(uint8_t* t_data, size_t t_width, size_t t_height, size_t t_size, size_t t_frameId);

    void on_actionConnect_triggered();

private:
    Ui::MainWindow *ui;

    std::unique_ptr<HVIS::EuresysFrameGrabber> m_frameGrabber;
    std::shared_ptr<spdlog::logger> m_logger;
};
#endif // MAINWINDOW_H
