#include "HVIS_Logging.h"
#include <iostream>
#include <thread>
#include <chrono>

int main() {
    try {
        std::cout << "Testing HVIS_Logging..." << std::endl;

        // Test 1: Basic initialization
        HVIS::HVISLogger::Config config;
        config.log_file = "test_logs/test.log";
        config.level = spdlog::level::trace;
        config.console_output = true;
        
        HVIS::HVISLogger::init("TestLogger", config);
        std::cout << "✓ Logger initialized successfully" << std::endl;

        // Test 2: Get logger and log messages
        auto logger = HVIS::HVISLogger::get("TestLogger");
        
        logger->trace("This is a trace message");
        logger->debug("This is a debug message");
        logger->info("This is an info message");
        logger->warn("This is a warning message");
        logger->error("This is an error message");
        logger->critical("This is a critical message");
        
        std::cout << "✓ All log levels tested" << std::endl;

        // Test 3: Multiple loggers
        HVIS::HVISLogger::Config config2;
        config2.log_file = "test_logs/module2.log";
        config2.level = spdlog::level::info;
        config2.console_output = false; // File only
        
        HVIS::HVISLogger::init("Module2", config2);
        auto logger2 = HVIS::HVISLogger::get("Module2");
        
        logger2->info("Module2 logger working");
        std::cout << "✓ Multiple loggers working" << std::endl;

        // Test 4: Formatted logging
        int value = 42;
        std::string name = "TestValue";
        logger->info("Formatted log: {} = {}", name, value);
        std::cout << "✓ Formatted logging working" << std::endl;

        // Test 5: Flush and shutdown
        HVIS::HVISLogger::flush_all();
        std::cout << "✓ Flush completed" << std::endl;

        // Wait a bit to ensure async logging completes
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        HVIS::HVISLogger::shutdown();
        std::cout << "✓ Shutdown completed" << std::endl;

        std::cout << "\nAll tests passed! Check test_logs/ directory for log files." << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
