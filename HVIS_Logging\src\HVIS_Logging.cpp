#include "HVIS_Logging.h"
// #include <backward.hpp>
#include <csignal>
#include <stdexcept>

namespace HVIS
{

namespace
{
    // backward::SignalHandling sh; // Crash handler for stack traces

    // Signal handler to flush logs and capture stack traces
    void crash_handler(int sig) {
        auto logger = HVISLogger::get("crash");
        if (logger) {
            logger->critical("Crash detected: Signal {}", sig);
            HVISLogger::flush_all();
        }
        // backward-cpp will print stack trace to stderr
        // Continue to default signal handling
    }

    // Register crash handlers for common signals
    void register_crash_handlers() {
        signal(SIGSEGV, crash_handler);
        signal(S<PERSON><PERSON><PERSON>, crash_handler);
        signal(<PERSON><PERSON><PERSON><PERSON>, crash_handler);
        signal(<PERSON><PERSON><PERSON><PERSON>, crash_handler);
        // signal(SIGB<PERSON>, crash_handler);
        signal(SIG<PERSON>R<PERSON>, crash_handler);
    }
} // anonymous namespace

void HVISLogger::init(const std::string& logger_name, const Config& config) {
    try {
        // Create sinks
        std::vector<spdlog::sink_ptr> sinks;

        // Console sink
        if (config.console_output) {
            auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
            console_sink->set_level(config.level);
            console_sink->set_pattern(config.pattern);
            sinks.push_back(console_sink);
        }

        // Rotating file sink
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            config.log_file, config.max_file_size, config.max_files);
        file_sink->set_level(config.level);
        file_sink->set_pattern(config.pattern);
        sinks.push_back(file_sink);

        // Create async logger
        auto logger = std::make_shared<spdlog::async_logger>(
            logger_name, sinks.begin(), sinks.end(), spdlog::thread_pool(),
            spdlog::async_overflow_policy::block);
        logger->set_level(config.level);
        logger->flush_on(spdlog::level::err); // Flush on errors

        // Register logger
        spdlog::register_logger(logger);

        // Create a default "crash" logger if not already created
        if (!spdlog::get("crash")) {
            Config crash_config = config;
            crash_config.log_file = "logs/crash.log";
            auto crash_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                crash_config.log_file, crash_config.max_file_size, crash_config.max_files);
            crash_sink->set_level(spdlog::level::critical);
            crash_sink->set_pattern(config.pattern);
            auto crash_logger = std::make_shared<spdlog::async_logger>(
                "crash", crash_sink, spdlog::thread_pool(),
                spdlog::async_overflow_policy::block);
            crash_logger->set_level(spdlog::level::critical);
            spdlog::register_logger(crash_logger);
        }

        // Register crash handlers (only once)
        static bool crash_handlers_registered = false;
        if (!crash_handlers_registered) {
            register_crash_handlers();
            crash_handlers_registered = true;
        }
    } catch (const spdlog::spdlog_ex& ex) {
        throw std::runtime_error("HVISLogger init failed: " + std::string(ex.what()));
    }
}

std::shared_ptr<spdlog::logger> HVISLogger::get(const std::string& logger_name) {
    auto logger = spdlog::get(logger_name);
    if (!logger) {
        throw std::runtime_error("Logger not found: " + logger_name);
    }
    return logger;
}

void HVISLogger::set_level(spdlog::level::level_enum level) {
    spdlog::set_level(level);
}

void HVISLogger::flush_all() {
    spdlog::apply_all([](const std::shared_ptr<spdlog::logger>& l) { l->flush(); });
}

void HVISLogger::shutdown() {
    spdlog::shutdown();
}

} // namespace HVIS