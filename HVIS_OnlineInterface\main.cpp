#include "mainwindow.h"

#include <QApplication>
#include <QLocale>
#include <QTranslator>
#include <QFile>

int main(int argc, char *argv[])
{
#ifdef _WIN32
    // Allocate a console for this application
    AllocConsole();
    // Redirect stdout to the console
    FILE* pConsole;
    freopen_s(&pConsole, "CONOUT$", "w", stdout);
    freopen_s(&pConsole, "CONOUT$", "w", stderr);
#endif

    QApplication a(argc, argv);

    // Set the app style sheet
    // QFile styleSheetFile("C:/repos/HexcelVisionApp/Toolery.qss");
    // styleSheetFile.open(QFile::ReadOnly);
    // QString styleSheet = QLatin1String(styleSheetFile.readAll());
    // a.setStyleSheet(styleSheet);

    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "HexcelVisionApp_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            a.installTranslator(&translator);
            break;
        }
    }
    MainWindow w;
    w.showMaximized();

    return a.exec();
}
