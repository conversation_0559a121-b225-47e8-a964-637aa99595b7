#include "mainwindow.h"
#include "HVIS_Logging.h"

#include <QApplication>
#include <QLocale>
#include <QTranslator>
#include <QFile>

int main(int argc, char *argv[])
{
#ifdef _WIN32
    // Allocate a console for this application
    AllocConsole();
    // Redirect stdout to the console
    FILE* pConsole;
    freopen_s(&pConsole, "CONOUT$", "w", stdout);
    freopen_s(&pConsole, "CONOUT$", "w", stderr);
#endif

    QApplication a(argc, argv);

    // Initialize HVIS_Logging
    try {
        HVIS::HVISLogger::Config config;
        config.log_file = "logs/hexcel_vision.log";
        config.level = spdlog::level::info;
        config.console_output = true;
        HVIS::HVISLogger::init("HexcelVision", config);

        auto logger = HVIS::HVISLogger::get("HexcelVision");
        logger->info("HexcelVision application starting...");
    } catch (const std::exception& e) {
        // Fallback if logging fails
        qDebug() << "Failed to initialize logging:" << e.what();
    }

    // Set the app style sheet
    // QFile styleSheetFile("C:/repos/HexcelVisionApp/Toolery.qss");
    // styleSheetFile.open(QFile::ReadOnly);
    // QString styleSheet = QLatin1String(styleSheetFile.readAll());
    // a.setStyleSheet(styleSheet);

    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "HexcelVisionApp_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            a.installTranslator(&translator);
            break;
        }
    }
    MainWindow w;
    w.showMaximized();

    int result = a.exec();

    // Shutdown logging before exit
    try {
        auto logger = HVIS::HVISLogger::get("HexcelVision");
        logger->info("HexcelVision application shutting down...");
        HVIS::HVISLogger::shutdown();
    } catch (const std::exception& e) {
        // Ignore errors during shutdown
    }

    return result;
}
