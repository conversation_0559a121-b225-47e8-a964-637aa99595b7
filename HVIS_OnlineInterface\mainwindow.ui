<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>640</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QGroupBox" name="groupBox">
      <property name="font">
       <font>
        <bold>false</bold>
       </font>
      </property>
      <property name="title">
       <string/>
      </property>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_10">
         <item>
          <spacer name="horizontalSpacer_12">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>February 04, 2025</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_8">
           <property name="text">
            <string>17:29 PM</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_13">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="1" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <item>
          <spacer name="horizontalSpacer_7">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>Product:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>P50E-black-test</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_8">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>Number of flaws:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>400</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_9">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>Line Speed (m/min):</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="lineSpeed">
           <property name="text">
            <string>7.3</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_10">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="2" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_9">
         <item>
          <spacer name="horizontalSpacer_14">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_9">
           <property name="text">
            <string>Roll Number:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_10">
           <property name="text">
            <string>6</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_15">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_11">
           <property name="text">
            <string>Roll Length:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_12">
           <property name="text">
            <string>180.85</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_16">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_13">
           <property name="text">
            <string>Width:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_14">
           <property name="text">
            <string>617.3</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_17">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="3" column="0">
        <layout class="QHBoxLayout" name="horizontalLayout_11">
         <item>
          <spacer name="horizontalSpacer_18">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="label_15">
           <property name="text">
            <string>Batch:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_16">
           <property name="text">
            <string>55M90123456Z</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_19">
           <property name="orientation">
            <enum>Qt::Orientation::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QStackedWidget" name="stackedWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="page">
       <layout class="QGridLayout" name="gridLayout" columnminimumwidth="0,0">
        <item row="0" column="1">
         <widget class="QGroupBox" name="groupBox_4">
          <property name="title">
           <string>Roll map (20 m) - Bottom View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QGroupBox" name="groupBox_3">
          <property name="title">
           <string>Roll map (20 m) - Top View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
          <layout class="QGridLayout" name="gridLayout_4">
           <item row="0" column="0">
            <widget class="QLabel" name="top20mImage">
             <property name="font">
              <font>
               <bold>true</bold>
               <underline>false</underline>
               <strikeout>false</strikeout>
              </font>
             </property>
             <property name="text">
              <string>NO CAMERA/VIDEO SIGNAL</string>
             </property>
             <property name="textFormat">
              <enum>Qt::TextFormat::AutoText</enum>
             </property>
             <property name="scaledContents">
              <bool>true</bool>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QGroupBox" name="groupBox_5">
          <property name="title">
           <string>Last Defects - Top View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_2" stretch="0,0">
             <property name="sizeConstraint">
              <enum>QLayout::SizeConstraint::SetDefaultConstraint</enum>
             </property>
             <item>
              <widget class="QLabel" name="lastDefectTop1">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_6">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>Position:
Size:
Class:
Confidence:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignmentFlag::AlignBottom|Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft</set>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <item>
              <widget class="QLabel" name="lastDefectTop2">
               <property name="text">
                <string/>
               </property>
               <property name="scaledContents">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_17">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>Position:
Size:
Class:
Confidence:</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <item>
              <widget class="QLabel" name="lastDefectTop3">
               <property name="text">
                <string/>
               </property>
               <property name="scaledContents">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_18">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="text">
                <string>Position:
Size:
Class:
Confidence:</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QGroupBox" name="groupBox_6">
          <property name="title">
           <string>Last Defects - Bottom View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_2">
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QGroupBox" name="groupBox_7">
          <property name="title">
           <string>Complete Roll Map - Top View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QGroupBox" name="groupBox_8">
          <property name="title">
           <string>Complete Roll Map - Bottom View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QGroupBox" name="groupBox_9">
          <property name="title">
           <string>Last Defects - Top View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QGroupBox" name="groupBox_10">
          <property name="title">
           <string>Last Defects - Bottom View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_3">
       <layout class="QGridLayout" name="gridLayout_5">
        <item row="0" column="0">
         <widget class="QGroupBox" name="groupBox_11">
          <property name="title">
           <string>Defect List - Top View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_4">
       <layout class="QGridLayout" name="gridLayout_6">
        <item row="0" column="0">
         <widget class="QGroupBox" name="groupBox_12">
          <property name="title">
           <string>Defect List - Bottom View</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignmentFlag::AlignCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="groupBox_2">
      <property name="title">
       <string/>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_6">
       <item>
        <widget class="QPushButton" name="previousPage">
         <property name="text">
          <string>&lt;</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_6">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton">
         <property name="text">
          <string>Production Info</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_5">
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="text">
          <string>Review File</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_2">
         <property name="text">
          <string>Lane Options</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_3">
         <property name="text">
          <string>Adjust Material Borders</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_4">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_4">
         <property name="text">
          <string>Roll Reset</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_5">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="playStandbyButton">
         <property name="text">
          <string>Play/Standby</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_11">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="nextPage">
         <property name="text">
          <string>&gt;</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1024</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <widget class="QMenu" name="menuSystem_Configuration">
     <property name="title">
      <string>System Configuration</string>
     </property>
     <addaction name="actionWindup_Configuration"/>
     <addaction name="actionMisc_Configurations"/>
     <addaction name="actionProcessing_Options"/>
    </widget>
    <widget class="QMenu" name="menuSystem_Tools">
     <property name="title">
      <string>System Tools</string>
     </property>
     <widget class="QMenu" name="menuBackup">
      <property name="title">
       <string>Backup</string>
      </property>
      <addaction name="actionBackup_Settings"/>
      <addaction name="actionBackup_System"/>
      <addaction name="actionBackup_System_Backup_path"/>
     </widget>
     <addaction name="actionShow_System_Info"/>
     <addaction name="actionShow_System_Statistics"/>
     <addaction name="separator"/>
     <addaction name="actionCalibration"/>
     <addaction name="separator"/>
     <addaction name="actionPseudo_Validation_Defects"/>
     <addaction name="actionOnline_Defect_Validation"/>
     <addaction name="actionGeometric_Validation_Check"/>
     <addaction name="menuBackup"/>
     <addaction name="actionLog_File"/>
    </widget>
    <widget class="QMenu" name="menuFile_Preferences">
     <property name="title">
      <string>File Preferences</string>
     </property>
     <addaction name="actionFile_Name_Format"/>
     <addaction name="actionFile_Locations"/>
     <addaction name="actionFile_Purge_Options"/>
     <addaction name="actionFlaw_File_Format"/>
     <addaction name="actionPDF_FIle_Format"/>
    </widget>
    <widget class="QMenu" name="menuPreferences">
     <property name="title">
      <string>Preferences</string>
     </property>
     <addaction name="actionCustomize_Button_Bar"/>
     <addaction name="actionCustomize_Functions"/>
     <addaction name="actionCustomize_Production_Information"/>
     <addaction name="actionCustomize_Screen"/>
     <addaction name="actionI_O_Options"/>
     <addaction name="actionLanguage"/>
    </widget>
    <addaction name="separator"/>
    <addaction name="actionReview_File"/>
    <addaction name="separator"/>
    <addaction name="actionCamera_Bank_Setup"/>
    <addaction name="menuSystem_Configuration"/>
    <addaction name="menuSystem_Tools"/>
    <addaction name="separator"/>
    <addaction name="actionSetup_Printer"/>
    <addaction name="actionPrint_Scrren"/>
    <addaction name="actionPDF_Screen"/>
    <addaction name="separator"/>
    <addaction name="menuFile_Preferences"/>
    <addaction name="menuPreferences"/>
    <addaction name="separator"/>
    <addaction name="actionExit_and_Shutdown_System"/>
    <addaction name="actionExit_and_Restart_System"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuScreen_Select">
    <property name="title">
     <string>Screen Select</string>
    </property>
    <addaction name="actionNext_Screen"/>
    <addaction name="actionPrevious_Screen"/>
   </widget>
   <widget class="QMenu" name="menuControl">
    <property name="title">
     <string>Control</string>
    </property>
    <addaction name="actionStart_Inspection_F3"/>
    <addaction name="actionStop_Inspection_F4"/>
    <addaction name="actionClear_Alarms_F5"/>
    <addaction name="actionRoll_Reset_F7"/>
   </widget>
   <widget class="QMenu" name="menuSetup">
    <property name="title">
     <string>Setup</string>
    </property>
    <addaction name="actionAlarm_Setup"/>
    <addaction name="actionInspection_Settings"/>
    <addaction name="actionImage_Processing_Setup"/>
    <addaction name="actionEdge_Analyzer_Setup"/>
    <addaction name="actionGraph_Options"/>
    <addaction name="actionSystem_Options"/>
    <addaction name="actionFlaw_Classifier_Options"/>
    <addaction name="actionLane_Setup"/>
    <addaction name="actionRoll_Segment_Options"/>
   </widget>
   <widget class="QMenu" name="menuProduct">
    <property name="title">
     <string>Product</string>
    </property>
    <addaction name="actionSet_Product_Information"/>
    <addaction name="actionSetup_Product"/>
    <addaction name="actionSetup_Product_Table"/>
   </widget>
   <widget class="QMenu" name="menuSecurity_Options">
    <property name="title">
     <string>Security</string>
    </property>
    <addaction name="actionSecurity_Options"/>
    <addaction name="actionLog_Out"/>
    <addaction name="actionOperators_Log_in"/>
    <addaction name="actionEngineers_Log_In"/>
    <addaction name="actionAdministrator_Log_In"/>
    <addaction name="actionMaster_Log_In"/>
   </widget>
   <widget class="QMenu" name="menuStreaming">
    <property name="title">
     <string>Streaming</string>
    </property>
    <addaction name="actionAcquire_Image_Stream"/>
    <addaction name="actionList_Image_Streams"/>
    <addaction name="actionImage_Stream_Setup"/>
   </widget>
   <widget class="QMenu" name="menuDebug">
    <property name="title">
     <string>Debug</string>
    </property>
    <addaction name="actionTBD"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>Help</string>
    </property>
    <addaction name="actionContents"/>
    <addaction name="actionAbout_this_program"/>
   </widget>
   <widget class="QMenu" name="menuFrame_Grabber">
    <property name="title">
     <string>Frame Grabber</string>
    </property>
    <addaction name="actionConnect"/>
    <addaction name="actionConfigure"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuScreen_Select"/>
   <addaction name="menuControl"/>
   <addaction name="menuSetup"/>
   <addaction name="menuFrame_Grabber"/>
   <addaction name="menuProduct"/>
   <addaction name="menuSecurity_Options"/>
   <addaction name="menuStreaming"/>
   <addaction name="menuDebug"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionReview_File">
   <property name="text">
    <string>Review File</string>
   </property>
  </action>
  <action name="actionCamera_Bank_Setup">
   <property name="text">
    <string>Camera Bank Setup</string>
   </property>
  </action>
  <action name="actionSetup_Printer">
   <property name="text">
    <string>Setup Printer</string>
   </property>
  </action>
  <action name="actionPrint_Scrren">
   <property name="text">
    <string>Print Screen</string>
   </property>
  </action>
  <action name="actionPDF_Screen">
   <property name="text">
    <string>PDF Screen</string>
   </property>
  </action>
  <action name="actionExit_and_Shutdown_System">
   <property name="text">
    <string>Exit and Shutdown System</string>
   </property>
  </action>
  <action name="actionExit_and_Restart_System">
   <property name="text">
    <string>Exit and Restart System</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
  </action>
  <action name="actionWindup_Configuration">
   <property name="text">
    <string>Windup Configuration</string>
   </property>
  </action>
  <action name="actionMisc_Configurations">
   <property name="text">
    <string>Misc Configurations</string>
   </property>
  </action>
  <action name="actionProcessing_Options">
   <property name="text">
    <string>Processing Options</string>
   </property>
  </action>
  <action name="actionShow_System_Info">
   <property name="text">
    <string>Show System Info</string>
   </property>
  </action>
  <action name="actionShow_System_Statistics">
   <property name="text">
    <string>Show System Statistics</string>
   </property>
  </action>
  <action name="actionCalibration">
   <property name="text">
    <string>Calibration</string>
   </property>
  </action>
  <action name="actionPseudo_Validation_Defects">
   <property name="text">
    <string>Pseudo Validation Defects</string>
   </property>
  </action>
  <action name="actionOnline_Defect_Validation">
   <property name="text">
    <string>Online Defect Validation</string>
   </property>
  </action>
  <action name="actionGeometric_Validation_Check">
   <property name="text">
    <string>Geometric Validation Check</string>
   </property>
  </action>
  <action name="actionLog_File">
   <property name="text">
    <string>Log File</string>
   </property>
  </action>
  <action name="actionBackup_Settings">
   <property name="text">
    <string>Backup Settings</string>
   </property>
  </action>
  <action name="actionBackup_System">
   <property name="text">
    <string>Backup System</string>
   </property>
  </action>
  <action name="actionBackup_System_Backup_path">
   <property name="text">
    <string>Backup System (Backup path)</string>
   </property>
  </action>
  <action name="actionFile_Name_Format">
   <property name="text">
    <string>File Name Format</string>
   </property>
  </action>
  <action name="actionFile_Locations">
   <property name="text">
    <string>File Locations</string>
   </property>
  </action>
  <action name="actionFile_Purge_Options">
   <property name="text">
    <string>File Purge Options</string>
   </property>
  </action>
  <action name="actionFlaw_File_Format">
   <property name="text">
    <string>Flaw File Format</string>
   </property>
  </action>
  <action name="actionPDF_FIle_Format">
   <property name="text">
    <string>PDF FIle Format</string>
   </property>
  </action>
  <action name="actionCustomize_Button_Bar">
   <property name="text">
    <string>Customize Button Bar</string>
   </property>
  </action>
  <action name="actionCustomize_Functions">
   <property name="text">
    <string>Customize Functions</string>
   </property>
  </action>
  <action name="actionCustomize_Production_Information">
   <property name="text">
    <string>Customize Production Information</string>
   </property>
  </action>
  <action name="actionCustomize_Screen">
   <property name="text">
    <string>Customize Screen</string>
   </property>
  </action>
  <action name="actionI_O_Options">
   <property name="text">
    <string>I/O Options</string>
   </property>
  </action>
  <action name="actionLanguage">
   <property name="text">
    <string>Language</string>
   </property>
  </action>
  <action name="actionNext_Screen">
   <property name="text">
    <string>Next Screen</string>
   </property>
  </action>
  <action name="actionPrevious_Screen">
   <property name="text">
    <string>Previous Screen</string>
   </property>
  </action>
  <action name="actionStart_Inspection_F3">
   <property name="text">
    <string>Start Inspection - F3</string>
   </property>
  </action>
  <action name="actionStop_Inspection_F4">
   <property name="text">
    <string>Stop Inspection - F4</string>
   </property>
  </action>
  <action name="actionClear_Alarms_F5">
   <property name="text">
    <string>Clear Alarms - F5</string>
   </property>
  </action>
  <action name="actionRoll_Reset_F7">
   <property name="text">
    <string>Roll Reset - F7</string>
   </property>
  </action>
  <action name="actionAlarm_Setup">
   <property name="text">
    <string>Alarm Setup</string>
   </property>
  </action>
  <action name="actionInspection_Settings">
   <property name="text">
    <string>Inspection Settings</string>
   </property>
  </action>
  <action name="actionImage_Processing_Setup">
   <property name="text">
    <string>Image Processing Setup</string>
   </property>
  </action>
  <action name="actionEdge_Analyzer_Setup">
   <property name="text">
    <string>Edge Analyzer Setup</string>
   </property>
  </action>
  <action name="actionGraph_Options">
   <property name="text">
    <string>Graph Options</string>
   </property>
  </action>
  <action name="actionSystem_Options">
   <property name="text">
    <string>System Options</string>
   </property>
  </action>
  <action name="actionFlaw_Classifier_Options">
   <property name="text">
    <string>Flaw Classifier Options</string>
   </property>
  </action>
  <action name="actionLane_Setup">
   <property name="text">
    <string>Lane Setup</string>
   </property>
  </action>
  <action name="actionRoll_Segment_Options">
   <property name="text">
    <string>Roll Segment Options</string>
   </property>
  </action>
  <action name="actionSet_Product_Information">
   <property name="text">
    <string>Set Product Information</string>
   </property>
  </action>
  <action name="actionSetup_Product">
   <property name="text">
    <string>Setup Product</string>
   </property>
  </action>
  <action name="actionSetup_Product_Table">
   <property name="text">
    <string>Setup Product Table</string>
   </property>
  </action>
  <action name="actionSecurity_Options">
   <property name="text">
    <string>Security Options</string>
   </property>
  </action>
  <action name="actionLog_Out">
   <property name="text">
    <string>Log Out</string>
   </property>
  </action>
  <action name="actionOperators_Log_in">
   <property name="text">
    <string>Operators Log In</string>
   </property>
  </action>
  <action name="actionEngineers_Log_In">
   <property name="text">
    <string>Engineers Log In</string>
   </property>
  </action>
  <action name="actionAdministrator_Log_In">
   <property name="text">
    <string>Administrator Log In</string>
   </property>
  </action>
  <action name="actionMaster_Log_In">
   <property name="text">
    <string>Master Log In</string>
   </property>
  </action>
  <action name="actionAcquire_Image_Stream">
   <property name="text">
    <string>Acquire Image Stream</string>
   </property>
  </action>
  <action name="actionList_Image_Streams">
   <property name="text">
    <string>List Image Streams</string>
   </property>
  </action>
  <action name="actionImage_Stream_Setup">
   <property name="text">
    <string>Image Stream Setup</string>
   </property>
  </action>
  <action name="actionTBD">
   <property name="text">
    <string>TBD</string>
   </property>
  </action>
  <action name="actionContents">
   <property name="text">
    <string>Contents</string>
   </property>
  </action>
  <action name="actionAbout_this_program">
   <property name="text">
    <string>About this program</string>
   </property>
  </action>
  <action name="actionConnect">
   <property name="text">
    <string>Connect</string>
   </property>
  </action>
  <action name="actionConfigure">
   <property name="text">
    <string>Configure</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
