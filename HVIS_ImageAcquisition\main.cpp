#include "EuresysFrameGrabber.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <memory>

#include "Logger.h"

// Simple callback function to process frames
void processFrame(void* bufferData, size_t width, size_t height, size_t size, size_t frameId)
{
    HVIS_LOG_DEBUG("Frame received: ID={}, Size={}, Resolution={}x{}", frameId, size, width, height);
    
    // Here you would typically process the image data
    // For example: display, save, or analyze the frame
}

int main()
{
    // Initialize global logger at the top of the file
    HVIS::Logging::Logger::getInstance().init("logs/image_acquisition.log");
    HVIS::Logging::Logger::getInstance().setLevel(spdlog::level::debug);

    try
    {
        HVIS_LOG_INFO("Starting image acquisition demo...");

        // Create a playlink grabber
        std::unique_ptr<HVIS::EuresysFrameGrabber> grabber = std::make_unique<HVIS::EuresysFrameGrabber>();
        
        // Initialize
        grabber->initialize(HVIS::EuresysFrameGrabber::EuresysProducer::Playlink);
        
        // Select camera
        grabber->selectCamera(1);

        // Set callback for frame processing
        grabber->setCallback(processFrame);
        
        // Allocate buffers
        grabber->allocBuffers(10);
        
        // Start acquisition
        HVIS_LOG_DEBUG("Starting acquisition...");
        grabber->startAcquisition();
        
        // Run for 5 seconds
        HVIS_LOG_DEBUG("Acquiring frames for 5 seconds...");
        std::this_thread::sleep_for(std::chrono::seconds(5));
        
        // Stop acquisition
        HVIS_LOG_DEBUG("Stopping acquisition...");
        grabber->stopAcquisition();
        
        HVIS_LOG_DEBUG("Demo completed successfully.");
    }
    catch (const std::exception& e)
    {
        HVIS_LOG_ERROR("Error: {}", e.what());
        return 1;
    }

    return 0;
}
