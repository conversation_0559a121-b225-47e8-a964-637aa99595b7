cmake_minimum_required(VERSION 3.16)

# Set policy to avoid duplicate target errors
if(POLICY CMP0002)
    cmake_policy(SET CMP0002 NEW)
endif()

project(HVIS_Suite VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set consistent output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Build order: HVIS_Logging first, then other modules
message(STATUS "Building HVIS Suite...")

# Build HVIS_Logging first (required by other modules)
add_subdirectory(HVIS_Logging)

# Build HVIS_ImageAcquisition
add_subdirectory(HVIS_ImageAcquisition)

# Build HVIS_OnlineInterface
add_subdirectory(HVIS_OnlineInterface)

message(STATUS "HVIS Suite configuration complete")
