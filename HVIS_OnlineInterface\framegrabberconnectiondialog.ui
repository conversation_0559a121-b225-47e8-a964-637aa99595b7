<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FrameGrabberConnectionDialog</class>
 <widget class="QDialog" name="FrameGrabberConnectionDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>550</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <property name="sizeGripEnabled">
   <bool>false</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,2,0">
       <property name="sizeConstraint">
        <enum>QLayout::SizeConstraint::SetDefaultConstraint</enum>
       </property>
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>Camera:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="cameraList">
         <property name="sizeAdjustPolicy">
          <enum>QComboBox::SizeAdjustPolicy::AdjustToContents</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="refreshButton">
         <property name="text">
          <string>Refresh List</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="2,0,1,0">
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Orientation::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Policy::Expanding</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label_2">
         <property name="text">
          <string>Producer:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="producerList">
         <item>
          <property name="text">
           <string>Coaxlink</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>Grablink</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>Gigelink</string>
          </property>
         </item>
         <item>
          <property name="text">
           <string>Playlink</string>
          </property>
         </item>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="connectButton">
         <property name="text">
          <string>Connect</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTextEdit" name="cameraConnectionOutput">
     <property name="readOnly">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::StandardButton::Cancel|QDialogButtonBox::StandardButton::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>FrameGrabberConnectionDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>FrameGrabberConnectionDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
