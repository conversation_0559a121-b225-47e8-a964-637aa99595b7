@echo off
echo Building HVIS Suite...

REM Create build directory
if not exist build mkdir build
cd build

REM Configure with CMake
echo Configuring CMake...
cmake .. -G "Visual Studio 17 2022" -A x64

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo.
echo Built targets:
echo - HVIS_Logging library
echo - ImageAcquisition library and executable
echo - HexcelVisionApp Qt application
echo.
echo Output files are in:
echo - Libraries: build\lib\Release\
echo - Executables: build\bin\Release\
echo.
pause
