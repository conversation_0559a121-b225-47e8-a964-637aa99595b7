// #ifndef FRAME_GRABBER_BASE_H
// #define FRAME_GRABBER_BASE_H

// #include <functional>
// #include <vector>
// #include <string>

// using CallbackFunc = std::function<void(void* bufferData, size_t width, size_t height, size_t size, size_t frameId)>;

// // Abstract base class for frame grabber implementations
// class FrameGrabberBase {
// public:
//     virtual ~FrameGrabberBase() = default;

//     // Allocate buffers for frame grabbing
//     virtual void allocBuffers(size_t numBuffers) = 0;

//     // Start image acquisition
//     virtual void startAcquisition() = 0;

//     // Stop image acquisition
//     virtual void stopAcquisition() = 0;

//     // Set callback for new buffer events
//     virtual void setCallback(CallbackFunc callback) = 0;

//     // Check if acquisition is started
//     virtual bool isAcquisitionStarted() const = 0;

//     // Check if frame grabber is initialized
//     virtual bool isInitialized() const = 0;

//     // Get list of discovered cameras (optional, can be empty for some implementations)
//     virtual std::vector<std::string> getCameraList() const = 0;
// };

// #endif // FRAME_GRABBER_BASE_H
