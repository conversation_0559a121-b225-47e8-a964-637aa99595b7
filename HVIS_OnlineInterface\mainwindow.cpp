#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "FrameGrabberConnectionDialog.h"
#include <QMessageBox>

#include <QDebug>
#include <thread>

#include <iostream>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);

    // TODO TESTING PART
    // Last 20 meters top
    // ui->top20mImage->setPixmap(QPixmap("C:/Users/<USER>/repos/HVIS_OnlineInterface/images/test8.1.jpeg"));
    ui->top20mImage->setSizePolicy(QSizePolicy::Ignored, QSizePolicy::Ignored);
    ui->top20mImage->setScaledContents(true);

    // Last defects top
    ui->lastDefectTop1->setPixmap(QPixmap("C:/Users/<USER>/repos/HVIS_OnlineInterface/images/df_1.jpeg"));
    ui->lastDefectTop1->setSizePolicy(QSizePolicy::Ignored, QSizePolicy::Ignored);
    ui->lastDefectTop1->setScaledContents(false);

    ui->lastDefectTop2->setPixmap(QPixmap("C:/Users/<USER>/repos/HVIS_OnlineInterface/images/df_2.jpeg"));
    ui->lastDefectTop2->setSizePolicy(QSizePolicy::Ignored, QSizePolicy::Ignored);
    ui->lastDefectTop2->setScaledContents(false);

    ui->lastDefectTop3->setPixmap(QPixmap("C:/Users/<USER>/repos/HVIS_OnlineInterface/images/df_3.jpeg"));
    ui->lastDefectTop3->setSizePolicy(QSizePolicy::Ignored, QSizePolicy::Ignored);
    ui->lastDefectTop3->setScaledContents(true);

    // I need to scale third image, if not i wont see anything since it is bigger than the other ones

    // Frame grabber init
    m_frameGrabber = std::make_unique<HVIS::EuresysFrameGrabber>();
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::on_previousPage_clicked()
{
    int currentIndex = ui->stackedWidget->currentIndex();

    if (currentIndex > 0)
        ui->stackedWidget->setCurrentIndex(currentIndex - 1);
    else
        qDebug() << "Impossible to go lower than 0";
}

void MainWindow::on_nextPage_clicked()
{
    int currentIndex = ui->stackedWidget->currentIndex();

    if (currentIndex < ui->stackedWidget->count() - 1)
        ui->stackedWidget->setCurrentIndex(currentIndex + 1);
    else
        qDebug() << "Impossible to go higher than the maximum";
}

void MainWindow::on_callbackFrameReceived(uint8_t* t_data, size_t t_width, size_t t_height, size_t t_size, size_t t_frameId)
{
    int stride = ((t_width * 3) + 1);

    std::cout << "Callback received with parameters:" << std::endl;
    std::cout << "  Data pointer: " << (void *)t_data << std::endl;
    std::cout << "  Width: " << t_width << std::endl;
    std::cout << "  Height: " << t_height << std::endl;
    std::cout << "  Stride: " << stride << std::endl;
    std::cout << "  Size: " << t_size << std::endl;
    std::cout << "  Frame ID: " << t_frameId << std::endl;
    
    // Assuming the data is in grayscale format (8-bit)
    QImage image(t_data, t_width, t_height, stride, QImage::Format_RGB888);
    
    // If the image needs to be copied because frameData might be freed after callback
    QImage imageCopy = image.copy();
    
    // Update image
    ui->top20mImage->setPixmap(QPixmap::fromImage(imageCopy));
}

void MainWindow::on_playStandbyButton_clicked()
{
    m_frameGrabber->allocBuffers(20);

    // Fix: Use std::bind to bind the member function to this instance
    auto callback = std::bind(&MainWindow::on_callbackFrameReceived, this,
                              std::placeholders::_1, std::placeholders::_2,
                              std::placeholders::_3, std::placeholders::_4,
                              std::placeholders::_5);

    m_frameGrabber->setCallback(callback);
    m_frameGrabber->startAcquisition();
    //m_frameGrabber->configureEuresysDevices();
}

void MainWindow::on_actionConnect_triggered()
{
    // Create a new dialog instance
    FrameGrabberConnectionDialog dialog(m_frameGrabber, this);
    
    // Set window title
    dialog.setWindowTitle("Frame Grabber Connection");
    
    // Show the dialog and wait for user interaction
    int result = dialog.exec();
    
    // Process the result
    if (result == QDialog::Accepted) {
        // User clicked OK
        std::cout << "Connection dialog accepted" << std::endl;
        
        // TODO: Get connection parameters from dialog and connect to frame grabber
        // This will be implemented once we add form fields to the dialog
    } else {
        // User clicked Cancel
        std::cout << "Connection dialog cancelled" << std::endl;
    }
}
