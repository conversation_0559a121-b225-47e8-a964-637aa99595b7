#include "FrameGrabberConnectionDialog.h"
#include "ui_framegrabberconnectiondialog.h"

#include <iostream>

FrameGrabberConnectionDialog::FrameGrabberConnectionDialog(std::unique_ptr<HVIS::EuresysFrameGrabber> &t_frameGrabber, QWidget *parent)
    : QDialog(parent)
    , ui(new Ui::FrameGrabberConnectionDialog)
    , m_frameGrabber(t_frameGrabber)
{
    ui->setupUi(this);
}

FrameGrabberConnectionDialog::~FrameGrabberConnectionDialog()
{
    delete ui;
}

void FrameGrabberConnectionDialog::on_refreshButton_clicked()
{
    // TODO: Refactor this function
    HVIS::EuresysFrameGrabber::EuresysProducer t_producer =
        static_cast<HVIS::EuresysFrameGrabber::EuresysProducer>(ui->producerList->currentIndex());

    this->setCursor(QCursor(Qt::CursorShape::WaitCursor));

    m_frameGrabber->initialize(t_producer);
    m_cameraList = m_frameGrabber->getCameraDescriptionList();
    std::vector<std::string> t_camDescription = m_frameGrabber->getCameraList();

    this->setCursor(QCursor(Qt::CursorShape::ArrowCursor));

    for (const auto& camString : m_cameraList)
    {
        ui->cameraConnectionOutput->append(QString::fromStdString(camString));
    }

    for (const auto& camDesc : t_camDescription)
    {
        ui->cameraList->addItem(QString::fromStdString(camDesc));
    }
}

