# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.31
cmake_policy(SET CMP0009 NEW)

# HEADERS at HVIS_ImageAcquisition/CMakeLists.txt:48 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/repos/HVIS/HVIS_ImageAcquisition/*.h")
set(OLD_GLOB
  "C:/Users/<USER>/repos/HVIS/HVIS_ImageAcquisition/include/EuresysFrameGrabber.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/cmake.verify_globs")
endif()

# HEADERS at HVIS_ImageAcquisition/CMakeLists.txt:48 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/repos/HVIS/HVIS_ImageAcquisition/include/*.h")
set(OLD_GLOB
  "C:/Users/<USER>/repos/HVIS/HVIS_ImageAcquisition/include/EuresysFrameGrabber.h"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/cmake.verify_globs")
endif()

# SOURCES at HVIS_ImageAcquisition/CMakeLists.txt:47 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "C:/Users/<USER>/repos/HVIS/HVIS_ImageAcquisition/src/*.cpp")
set(OLD_GLOB
  "C:/Users/<USER>/repos/HVIS/HVIS_ImageAcquisition/src/EuresysFrameGrabber.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "C:/Users/<USER>/repos/HVIS/build/CMakeFiles/cmake.verify_globs")
endif()
