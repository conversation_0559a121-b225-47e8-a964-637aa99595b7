set(__QT_DEPLOY_TARGET_HVIS_Logging_FILE C:/Users/<USER>/repos/HVIS/build/lib/libhvis_logging.a)
set(__QT_DEPLOY_TARGET_HVIS_Logging_TYPE STATIC_LIBRARY)
set(__QT_DEPLOY_TARGET_ImageAcquisition_FILE C:/Users/<USER>/repos/HVIS/build/bin/libImageAcquisition.dll)
set(__QT_DEPLOY_TARGET_ImageAcquisition_TYPE SHARED_LIBRARY)
set(__QT_DEPLOY_TARGET_ImageAcquisition_RUNTIME_DLLS )
set(__QT_DEPLOY_TARGET_ImageAcquisitionApp_FILE C:/Users/<USER>/repos/HVIS/build/bin/ImageAcquisitionApp.exe)
set(__QT_DEPLOY_TARGET_ImageAcquisitionApp_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_ImageAcquisitionApp_RUNTIME_DLLS C:/Users/<USER>/repos/HVIS/build/bin/libImageAcquisition.dll)
set(__QT_DEPLOY_TARGET_HexcelVisionApp_FILE C:/Users/<USER>/repos/HVIS/build/bin/HexcelVisionApp.exe)
set(__QT_DEPLOY_TARGET_HexcelVisionApp_TYPE EXECUTABLE)
set(__QT_DEPLOY_TARGET_HexcelVisionApp_RUNTIME_DLLS C:/Qt_6/6.9.0/msvc2022_64/bin/Qt6Widgets.dll;C:/Qt_6/6.9.0/msvc2022_64/bin/Qt6Gui.dll;C:/Qt_6/6.9.0/msvc2022_64/bin/Qt6Core.dll)
