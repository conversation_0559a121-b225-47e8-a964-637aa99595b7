<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>FrameGrabberConfigurationDialog</class>
 <widget class="QDialog" name="FrameGrabberConfigurationDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="1" column="0">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Orientation::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::StandardButton::Cancel|QDialogButtonBox::StandardButton::Ok</set>
     </property>
    </widget>
   </item>
   <item row="0" column="0">
    <widget class="QTreeWidget" name="treeConfig">
     <property name="rootIsDecorated">
      <bool>true</bool>
     </property>
     <property name="uniformRowHeights">
      <bool>false</bool>
     </property>
     <property name="sortingEnabled">
      <bool>false</bool>
     </property>
     <column>
      <property name="text">
       <string>Port</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>Value</string>
      </property>
     </column>
     <item>
      <property name="text">
       <string>Stream Port</string>
      </property>
      <item>
       <property name="text">
        <string>ScanLength</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>BufferHeight</string>
       </property>
      </item>
     </item>
     <item>
      <property name="text">
       <string>Remote Port</string>
      </property>
      <item>
       <property name="text">
        <string>LedSelector</string>
       </property>
      </item>
     </item>
     <item>
      <property name="text">
       <string>Interface Port</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <item>
       <property name="text">
        <string>LineSelector</string>
       </property>
      </item>
      <item>
       <property name="text">
        <string>CxpRevisionSelector</string>
       </property>
       <property name="text">
        <string/>
       </property>
      </item>
     </item>
     <item>
      <property name="text">
       <string>Device Port</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <item>
       <property name="text">
        <string>CameraControlMethod</string>
       </property>
      </item>
     </item>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>FrameGrabberConfigurationDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>FrameGrabberConfigurationDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
