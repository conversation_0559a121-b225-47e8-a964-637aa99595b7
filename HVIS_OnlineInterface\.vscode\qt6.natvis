<?xml version="1.0" encoding="utf-8"?>
<AutoVisualizer xmlns="http://schemas.microsoft.com/vstudio/debugger/natvis/2010">
  <!-- Qt6 Visualizers -->
  <Type Name="QString">
    <DisplayString>{((reinterpret_cast&lt;unsigned short*&gt;(d_ptr.d)) + d_ptr.d->offset), su}</DisplayString>
    <StringView>((reinterpret_cast&lt;unsigned short*&gt;(d_ptr.d)) + d_ptr.d->offset), su</StringView>
  </Type>
  <Type Name="QByteArray">
    <DisplayString>{((char*)(d.data)) + d.offset}, s8</DisplayString>
  </Type>
  <Type Name="QList&lt;*&gt;">
    <DisplayString>{{ size = {d.size} }}</DisplayString>
    <Expand>
      <Item Name="[size]">d.size</Item>
      <ArrayItems>
        <Size>d.size</Size>
        <ValuePointer>($T1*)(d.begin())</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="QVector&lt;*&gt;">
    <DisplayString>{{ size = {d.size} }}</DisplayString>
    <Expand>
      <Item Name="[size]">d.size</Item>
      <ArrayItems>
        <Size>d.size</Size>
        <ValuePointer>($T1*)(d.begin())</ValuePointer>
      </ArrayItems>
    </Expand>
  </Type>
  <Type Name="QMap&lt;*,*&gt;">
    <DisplayString>{{ size = {d.size} }}</DisplayString>
    <Expand>
      <Item Name="[size]">d.size</Item>
      <Item Name="[forward]">d.forward</Item>
      <TreeItems>
        <Size>d.size</Size>
        <HeadPointer>d.header.left</HeadPointer>
        <LeftPointer>left</LeftPointer>
        <RightPointer>right</RightPointer>
        <ValueNode>
          <Item Name="[key]">key</Item>
          <Item Name="[value]">value</Item>
        </ValueNode>
      </TreeItems>
    </Expand>
  </Type>
</AutoVisualizer>