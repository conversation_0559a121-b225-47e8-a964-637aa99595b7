#ifndef LOGGER_H
#define LOGGER_H

#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include <memory>
#include <string>

namespace HVIS {
namespace Logging {

class Logger {
public:
    // Get the singleton instance of the logger
    static Logger& getInstance() {
        static Logger instance;
        return instance;
    }

    // Initialize the logger with console and optional file output
    void init(const std::string& logFilePath = "", size_t maxFileSize = 5 * 1024 * 1024, size_t maxFiles = 3) {
        try {
            // Create console sink
            auto consoleSink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
            consoleSink->set_level(spdlog::level::trace);
            consoleSink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] [%s:%!:%#] %v");

            std::vector<spdlog::sink_ptr> sinks{consoleSink};

            // Create file sink if logFilePath is provided
            if (!logFilePath.empty()) {
                auto fileSink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                    logFilePath, maxFileSize, maxFiles);
                fileSink->set_level(spdlog::level::trace);
                fileSink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%s:%!:%#] %v");
                sinks.push_back(fileSink);
            }

            // Create logger with sinks
            logger_ = std::make_shared<spdlog::logger>("HVIS_Logger", sinks.begin(), sinks.end());
            logger_->set_level(spdlog::level::trace);
            logger_->flush_on(spdlog::level::info); // Flush on info or higher

            // Set as default logger to ensure it's used across all libraries
            spdlog::set_default_logger(logger_);
            
            // Register logger to make it accessible by name
            spdlog::register_logger(logger_);
            
            // Log initialization
            logger_->info("Logger initialized");
        } catch (const spdlog::spdlog_ex& ex) {
            std::cerr << "Logger initialization failed: " << ex.what() << std::endl;
        }
    }

    // Get the logger directly
    std::shared_ptr<spdlog::logger> getLogger() {
        // Auto-initialize if not already done
        if (!logger_) {
            init();
        }
        return logger_;
    }

    // Set log level
    void setLevel(spdlog::level::level_enum level) {
        // Auto-initialize if not already done
        if (!logger_) {
            init();
        }
        
        logger_->set_level(level);
        
        // Also set the global level for spdlog
        spdlog::set_level(level);
        
        // Set level for all sinks
        for (const auto& sink : logger_->sinks()) {
            sink->set_level(level);
        }
        
        // Log the level change if appropriate
        if (level <= spdlog::level::info) {
            logger_->info("Log level changed to: {}", spdlog::level::to_string_view(level));
        }
    }

    // Logging functions with auto-initialization
    template<typename... Args>
    void trace(const Args&... args) {
        if (!logger_) init();
        logger_->trace(args...);
    }

    template<typename... Args>
    void debug(const Args&... args) {
        if (!logger_) init();
        logger_->debug(args...);
    }

    template<typename... Args>
    void info(const Args&... args) {
        if (!logger_) init();
        logger_->info(args...);
    }

    template<typename... Args>
    void warn(const Args&... args) {
        if (!logger_) init();
        logger_->warn(args...);
    }

    template<typename... Args>
    void error(const Args&... args) {
        if (!logger_) init();
        logger_->error(args...);
    }

    template<typename... Args>
    void critical(const Args&... args) {
        if (!logger_) init();
        logger_->critical(args...);
    }

private:
    Logger() = default;
    ~Logger() {
        if (logger_) {
            logger_->info("Logger shutting down");
            logger_.reset();
            spdlog::drop_all();
        }
    }

    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;

    std::shared_ptr<spdlog::logger> logger_;
};

} // namespace Logging
} // namespace HVIS

// Convenience macros that ensure logger is initialized
#define HVIS_LOG_TRACE(...) HVIS::Logging::Logger::getInstance().trace(__VA_ARGS__)
#define HVIS_LOG_DEBUG(...) HVIS::Logging::Logger::getInstance().debug(__VA_ARGS__)
#define HVIS_LOG_INFO(...)  HVIS::Logging::Logger::getInstance().info(__VA_ARGS__)
#define HVIS_LOG_WARN(...)  HVIS::Logging::Logger::getInstance().warn(__VA_ARGS__)
#define HVIS_LOG_ERROR(...) HVIS::Logging::Logger::getInstance().error(__VA_ARGS__)
#define HVIS_LOG_CRITICAL(...) HVIS::Logging::Logger::getInstance().critical(__VA_ARGS__)

// Convenience functions for setting log levels
#define HVIS_LOG_SET_TRACE() HVIS::Logging::Logger::getInstance().setLevel(spdlog::level::trace)
#define HVIS_LOG_SET_DEBUG() HVIS::Logging::Logger::getInstance().setLevel(spdlog::level::debug)
#define HVIS_LOG_SET_INFO() HVIS::Logging::Logger::getInstance().setLevel(spdlog::level::info)
#define HVIS_LOG_SET_WARN() HVIS::Logging::Logger::getInstance().setLevel(spdlog::level::warn)
#define HVIS_LOG_SET_ERROR() HVIS::Logging::Logger::getInstance().setLevel(spdlog::level::err)
#define HVIS_LOG_SET_CRITICAL() HVIS::Logging::Logger::getInstance().setLevel(spdlog::level::critical)

#endif // LOGGER_H
