cmake_minimum_required(VERSION 3.19)
project(HexcelVisionApp LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Helping VScode finding Qt6
set(Qt6_DIR "C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6")

# Qt setup
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets LinguistTools)

# Find spdlog (for HVIS_Logging dependency)
find_package(spdlog QUIET)

# Try to find HVIS_Logging first, if not found, build it as subdirectory
find_package(HVIS_Logging QUIET)
if(NOT HVIS_Logging_FOUND)
    # Add HVIS_Logging as subdirectory if not found as installed package
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../HVIS_Logging/CMakeLists.txt")
        add_subdirectory("${CMAKE_CURRENT_SOURCE_DIR}/../HVIS_Logging" HVIS_Logging_build)
        message(STATUS "Building HVIS_Logging from source")
    else()
        message(FATAL_ERROR "HVIS_Logging not found and source directory not available")
    endif()
else()
    message(STATUS "Found installed HVIS_Logging")
endif()

# Include eGrabber libs
include_directories("C:/Program Files/Euresys/eGrabber/include")

qt_standard_project_setup()

qt_add_executable(HexcelVisionApp
    WIN32 MACOSX_BUNDLE
    main.cpp
    mainwindow.cpp
    mainwindow.h
    mainwindow.ui
    EuresysFrameGrabber.h
    EuresysFrameGrabber.cpp
    FrameGrabberBase.h
    mainwindow._2ui
    FrameGrabberConnectionDialog.h FrameGrabberConnectionDialog.cpp framegrabberconnectiondialog.ui
    FrameGrabberConfigurationDialog.h FrameGrabberConfigurationDialog.cpp framegrabberconfigurationdialog.ui

)

qt_add_translations(
    TARGETS HexcelVisionApp
    TS_FILES HexcelVisionApp_es_ES.ts
)

target_link_libraries(HexcelVisionApp
    PRIVATE
        Qt::Core
        Qt::Widgets
        HVIS::Logging
)

include(GNUInstallDirs)
install(TARGETS HexcelVisionApp
    BUNDLE  DESTINATION .
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
)

qt_generate_deploy_app_script(
    TARGET HexcelVisionApp
    OUTPUT_SCRIPT deploy_script
    NO_UNSUPPORTED_PLATFORM_ERROR
)
install(SCRIPT ${deploy_script})


# Use windeployqt to copy Qt dependencies
if(WIN32)
    add_custom_command(TARGET HexcelVisionApp POST_BUILD
        COMMAND ${Qt6_DIR}/../../../bin/windeployqt.exe $<TARGET_FILE:HexcelVisionApp>
    )
endif()
