#ifndef EURESYS_FRAME_GRABBER_H
#define EURESYS_FRAME_GRABBER_H

// #include "FrameGrabberBase.h"
#include <memory>
#include <functional>

#include <EGrabber.h>

namespace HVIS
{
namespace Internal
{
    class EuresysFrameGrabberInternal;

    // Callback pattern
    using CallbackFunc = std::function<void(uint8_t* bufferData, size_t width, size_t height, size_t size, size_t frameId)>;
}

// NOTE: decide if a base class has sense there
// Euresys-specific frame grabber implementation
class EuresysFrameGrabber
{
public:
    enum class EuresysProducer
    {
        Coaxlink,
        Grablink,
        Gigelink,
        Playlink
    };

    // Constructor: Automatically discovers cameras and selects one
    EuresysFrameGrabber();

    // Constructor: Allows user to specify camera by index
    // explicit EuresysFrameGrabber(size_t cameraIndex);

    // Destructor
    ~EuresysFrameGrabber();

    // Initialize GenTL and discovery
    void initialize(EuresysProducer t_producer);

    // Select camera based on index or user input
    void selectCamera(size_t cameraIndex = SIZE_MAX);

    // Allocate buffers
    void allocBuffers(size_t numBuffers);

    // Start image acquisition
    void startAcquisition();

    // Stop image acquisition
    void stopAcquisition();

    // Set callback for new buffer events
    void setCallback(Internal::CallbackFunc callback);

    // Check if acquisition is started
    bool isAcquisitionStarted() const;

    // Check if frame grabber is initialized
    bool isInitialized() const;

    // Get list of discovered cameras
    std::vector<std::string> getCameraList() const;

    // Get a description list for all discovered cameras
    std::vector<std::string> getCameraDescriptionList() const;

    // Configure Euresys devices
    void configureEuresysDevices();

private:
    std::unique_ptr<Euresys::EGenTL> m_genTL;
    std::unique_ptr<Euresys::EGrabberDiscovery> m_discovery;
    std::unique_ptr<Internal::EuresysFrameGrabberInternal> m_frameGrabber;
    std::vector<Euresys::EGrabberCameraInfo> m_cameras;
};
} // namespace HVIS
#endif // EURESYS_FRAME_GRABBER_H
