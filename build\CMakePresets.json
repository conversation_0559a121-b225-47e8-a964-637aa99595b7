{"version": 3, "vendor": {"conan": {}}, "cmakeMinimumRequired": {"major": 3, "minor": 15, "patch": 0}, "configurePresets": [{"name": "conan-default", "displayName": "'conan-default' config", "description": "'conan-default' configure using 'Visual Studio 17 2022' generator", "generator": "Visual Studio 17 2022", "cacheVariables": {"CMAKE_POLICY_DEFAULT_CMP0091": "NEW"}, "toolset": {"value": "v143", "strategy": "external"}, "architecture": {"value": "x64", "strategy": "external"}, "toolchainFile": "conan_toolchain.cmake", "binaryDir": "C:\\Users\\<USER>\\repos\\HVIS\\build"}], "buildPresets": [{"name": "conan-release", "configurePreset": "conan-default", "configuration": "Release", "jobs": 16}], "testPresets": [{"name": "conan-release", "configurePreset": "conan-default", "configuration": "Release", "environment": {"PATH": "C:\\Users\\<USER>\\.conan2\\p\\b\\spdloa0cb09ddbe052\\p\\bin;$penv{PATH}"}}]}