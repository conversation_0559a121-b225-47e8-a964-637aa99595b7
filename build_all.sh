#!/bin/bash

echo "Building HVIS Suite..."

# Create build directory
mkdir -p build
cd build

# Install Conan dependencies
echo "Installing Conan dependencies..."
conan install .. --output-folder=. --build=missing --settings=build_type=Release

if [ $? -ne 0 ]; then
    echo "Conan install failed! Make sure <PERSON> is installed and configured."
    exit 1
fi

# Configure with CMake
echo "Configuring CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake

if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

# Build the project
echo "Building project..."
cmake --build . --config Release

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Build completed successfully!"
echo ""
echo "Built targets:"
echo "- HVIS_Logging library"
echo "- ImageAcquisition library and executable"
echo "- HexcelVisionApp Qt application"
echo ""
echo "Output files are in:"
echo "- Libraries: build/lib/"
echo "- Executables: build/bin/"
echo ""
