#pragma once

#include <string>
#include <memory>
#include "spdlog/spdlog.h"
#include "spdlog/async.h"
#include "spdlog/sinks/rotating_file_sink.h"
#include "spdlog/sinks/stdout_color_sinks.h"

namespace HVIS
{

class HVISLogger
{
public:
    // Configuration struct for logger setup
    struct Config
    {
        std::string log_file = "logs/hvis.log"; // Default log file path
        size_t max_file_size = 5 * 1024 * 1024; // 5 MB
        size_t max_files = 3;                   // Keep 3 rotated files
        spdlog::level::level_enum level = spdlog::level::info; // Default log level
        std::string pattern = "[%Y-%m-%d %H:%M:%S.%e][%n][%l][%t] %v"; // Log format
        bool console_output = true; // Enable console output
    };

    // Initialize a logger with a unique name and optional configuration
    static void init(const std::string& logger_name, const Config& config = Config());

    // Get a logger instance by name
    static std::shared_ptr<spdlog::logger> get(const std::string& logger_name);

    // Set global log level
    static void set_level(spdlog::level::level_enum level);

    // Flush all loggers
    static void flush_all();

    // Shutdown logging system (flushes and closes all loggers)
    static void shutdown();

private:
    // Prevent instantiation
    HVISLogger() = delete;
};

} // namespace HVIS