# HVIS Suite - Integrated Logging

This repository contains the HVIS (Hexcel Vision Inspection System) suite with integrated logging across all modules.

## Modules

### HVIS_Logging
The central logging module that provides:
- Async logging with spdlog
- Rotating file logs
- Console output
- Crash handling and stack traces
- Multiple logger instances support

### HVIS_ImageAcquisition
Image acquisition module that now uses HVIS_Logging:
- Maintains backward compatibility with existing Logger.h interface
- Uses HVIS_Logging internally for consistent logging across the suite

### HVIS_OnlineInterface
Qt-based user interface that integrates HVIS_Logging:
- Logs user interactions and application events
- Demonstrates proper logging initialization and shutdown

## Building

### Quick Build (Recommended)
Use the provided build scripts:

**Windows:**
```cmd
build_all.bat
```

**Linux/Mac:**
```bash
chmod +x build_all.sh
./build_all.sh
```

### Manual Build - All Modules Together
```bash
# Install dependencies with Conan
mkdir build
cd build
conan install .. --output-folder=. --build=missing --settings=build_type=Release

# Configure and build with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake
cmake --build . --config Release
```

### Build Individual Modules
Each module can still be built independently:

```bash
# Build HVIS_Logging
cd HVIS_Logging
mkdir build && cd build
conan install .. --output-folder=. --build=missing
cmake .. -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake
cmake --build .

# Build HVIS_ImageAcquisition (requires HVIS_Logging)
cd HVIS_ImageAcquisition
mkdir build && cd build
conan install .. --output-folder=. --build=missing
cmake .. -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake
cmake --build .

# Build HVIS_OnlineInterface (requires HVIS_Logging)
cd HVIS_OnlineInterface
mkdir build && cd build
conan install .. --output-folder=. --build=missing
cmake .. -DCMAKE_TOOLCHAIN_FILE=conan_toolchain.cmake
cmake --build .
```

## Using HVIS_Logging

### Basic Usage

```cpp
#include "HVIS_Logging.h"

// Initialize logger
HVIS::HVISLogger::Config config;
config.log_file = "logs/my_module.log";
config.level = spdlog::level::info;
config.console_output = true;
HVIS::HVISLogger::init("MyModule", config);

// Get logger instance
auto logger = HVIS::HVISLogger::get("MyModule");

// Log messages
logger->info("Application started");
logger->warn("This is a warning");
logger->error("An error occurred: {}", error_message);

// Shutdown (optional, done automatically at exit)
HVIS::HVISLogger::shutdown();
```

### Configuration Options

```cpp
HVIS::HVISLogger::Config config;
config.log_file = "logs/app.log";           // Log file path
config.max_file_size = 5 * 1024 * 1024;     // 5 MB per file
config.max_files = 3;                       // Keep 3 rotated files
config.level = spdlog::level::info;         // Log level
config.pattern = "[%Y-%m-%d %H:%M:%S.%e][%n][%l][%t] %v"; // Log format
config.console_output = true;               // Enable console output
```

### Log Levels
- `trace`: Very detailed information
- `debug`: Debug information
- `info`: General information
- `warn`: Warning messages
- `error`: Error messages
- `critical`: Critical errors

## Integration Status

- ✅ **HVIS_Logging**: Core logging module
- ✅ **HVIS_ImageAcquisition**: Integrated with backward compatibility
- ✅ **HVIS_OnlineInterface**: Integrated with Qt application
- 🔄 **Future modules**: Ready for integration

## Dependencies

All dependencies are managed through Conan:
- **spdlog/1.15.1**: High-performance logging library
- **backward-cpp/1.6**: Stack trace support
- **gtest/1.16.0**: Testing framework
- **Qt6**: For HVIS_OnlineInterface (system installation required)
- **CMake 3.16+**: Build system
- **Conan 2.x**: Package manager

## Log Files

By default, logs are written to:
- `logs/hvis.log` - Default log file
- `logs/crash.log` - Crash logs
- `logs/hexcel_vision.log` - Qt application logs
- `logs/imageacquisition.log` - Image acquisition logs (if configured)

## Troubleshooting

### CMP0002 Policy Errors
If you encounter "CMP0002" policy errors about duplicate targets, this has been fixed by:
- Adding policy settings to all CMakeLists.txt files
- Adding target existence checks before creating subdirectories
- Using unique target names and aliases

### Build Issues
1. **Conan not found**: Install Conan 2.x: `pip install conan`
2. **Missing dependencies**: Run `conan install` manually if build scripts fail
3. **Qt6 not found**: Ensure Qt6 is installed and `Qt6_DIR` is set correctly in HVIS_OnlineInterface/CMakeLists.txt
4. **Euresys libraries**: Update the path in CMakeLists.txt files if Euresys is installed elsewhere

### Conan Setup
First time setup:
```bash
# Install Conan
pip install conan

# Create default profile
conan profile detect --force
```

### Individual Module Builds
Each module can be built independently and will automatically fetch HVIS_Logging as needed.

## Notes

- All modules now use consistent logging
- Existing code using the old Logger.h interface continues to work
- Crash handling is automatically enabled
- Log files are automatically rotated when they reach the size limit
- CMake policies are properly set to avoid target conflicts
