# HVIS Suite - Integrated Logging

This repository contains the HVIS (Hexcel Vision Inspection System) suite with integrated logging across all modules.

## Modules

### HVIS_Logging
The central logging module that provides:
- Async logging with spdlog
- Rotating file logs
- Console output
- Crash handling and stack traces
- Multiple logger instances support

### HVIS_ImageAcquisition
Image acquisition module that now uses HVIS_Logging:
- Maintains backward compatibility with existing Logger.h interface
- Uses HVIS_Logging internally for consistent logging across the suite

### HVIS_OnlineInterface
Qt-based user interface that integrates HVIS_Logging:
- Logs user interactions and application events
- Demonstrates proper logging initialization and shutdown

## Building

### Build All Modules Together
```bash
mkdir build
cd build
cmake ..
cmake --build .
```

### Build Individual Modules
Each module can still be built independently:

```bash
# Build HVIS_Logging
cd HVIS_Logging
mkdir build && cd build
cmake ..
cmake --build .

# Build HVIS_ImageAcquisition (requires HVIS_Logging)
cd HVIS_ImageAcquisition
mkdir build && cd build
cmake ..
cmake --build .

# Build HVIS_OnlineInterface (requires HVIS_Logging)
cd HVIS_OnlineInterface
mkdir build && cd build
cmake ..
cmake --build .
```

## Using HVIS_Logging

### Basic Usage

```cpp
#include "HVIS_Logging.h"

// Initialize logger
HVIS::HVISLogger::Config config;
config.log_file = "logs/my_module.log";
config.level = spdlog::level::info;
config.console_output = true;
HVIS::HVISLogger::init("MyModule", config);

// Get logger instance
auto logger = HVIS::HVISLogger::get("MyModule");

// Log messages
logger->info("Application started");
logger->warn("This is a warning");
logger->error("An error occurred: {}", error_message);

// Shutdown (optional, done automatically at exit)
HVIS::HVISLogger::shutdown();
```

### Configuration Options

```cpp
HVIS::HVISLogger::Config config;
config.log_file = "logs/app.log";           // Log file path
config.max_file_size = 5 * 1024 * 1024;     // 5 MB per file
config.max_files = 3;                       // Keep 3 rotated files
config.level = spdlog::level::info;         // Log level
config.pattern = "[%Y-%m-%d %H:%M:%S.%e][%n][%l][%t] %v"; // Log format
config.console_output = true;               // Enable console output
```

### Log Levels
- `trace`: Very detailed information
- `debug`: Debug information
- `info`: General information
- `warn`: Warning messages
- `error`: Error messages
- `critical`: Critical errors

## Integration Status

- ✅ **HVIS_Logging**: Core logging module
- ✅ **HVIS_ImageAcquisition**: Integrated with backward compatibility
- ✅ **HVIS_OnlineInterface**: Integrated with Qt application
- 🔄 **Future modules**: Ready for integration

## Dependencies

- **spdlog**: High-performance logging library
- **backward-cpp**: Stack trace support (optional)
- **Qt6**: For HVIS_OnlineInterface
- **CMake 3.16+**: Build system

## Log Files

By default, logs are written to:
- `logs/hvis.log` - Default log file
- `logs/crash.log` - Crash logs
- `logs/hexcel_vision.log` - Qt application logs
- `logs/imageacquisition.log` - Image acquisition logs (if configured)

## Notes

- All modules now use consistent logging
- Existing code using the old Logger.h interface continues to work
- Crash handling is automatically enabled
- Log files are automatically rotated when they reach the size limit
