########## MACROS ###########################################################################
#############################################################################################

# Requires CMake > 3.15
if(${CMAKE_VERSION} VERSION_LESS "3.15")
    message(FATAL_ERROR "The 'CMakeDeps' generator only works with CMake >= 3.15")
endif()

if(Backward_FIND_QUIETLY)
    set(Backward_MESSAGE_MODE VERBOSE)
else()
    set(Backward_MESSAGE_MODE STATUS)
endif()

include(${CMAKE_CURRENT_LIST_DIR}/cmakedeps_macros.cmake)
include(${CMAKE_CURRENT_LIST_DIR}/BackwardTargets.cmake)
include(CMakeFindDependencyMacro)

check_build_type_defined()

foreach(_DEPENDENCY ${backward-cpp_FIND_DEPENDENCY_NAMES} )
    # Check that we have not already called a find_package with the transitive dependency
    if(NOT ${_DEPENDENCY}_FOUND)
        find_dependency(${_DEPENDENCY} REQUIRED ${${_DEPENDENCY}_FIND_MODE})
    endif()
endforeach()

set(Backward_VERSION_STRING "1.6")
set(Backward_INCLUDE_DIRS ${backward-cpp_INCLUDE_DIRS_RELEASE} )
set(Backward_INCLUDE_DIR ${backward-cpp_INCLUDE_DIRS_RELEASE} )
set(Backward_LIBRARIES ${backward-cpp_LIBRARIES_RELEASE} )
set(Backward_DEFINITIONS ${backward-cpp_DEFINITIONS_RELEASE} )


# Only the last installed configuration BUILD_MODULES are included to avoid the collision
foreach(_BUILD_MODULE ${backward-cpp_BUILD_MODULES_PATHS_RELEASE} )
    message(${Backward_MESSAGE_MODE} "Conan: Including build module from '${_BUILD_MODULE}'")
    include(${_BUILD_MODULE})
endforeach()


