cmake_minimum_required(VERSION 3.16)

# Set policy to avoid duplicate target errors
if(POLICY CMP0002)
    cmake_policy(SET CMP0002 NEW)
endif()

project(HVIS_ImageAcquisition LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set consistent output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Check if conan libs are generated
# set(CONAN_LIBS "${CMAKE_CURRENT_SOURCE_DIR}/build/generators")
# if (NOT EXISTS ${CONAN_LIBS})
#     message(STATUS "Conan libs not generated. Please run 'conan install . --output-folder=build --build=missing' first")
# else()
#     message(STATUS "Found Conan libs at ${CONAN_LIBS}")
#     list(APPEND CMAKE_PREFIX_PATH ${CONAN_LIBS})
# endif()

# Find packages

# Try to find HVIS_Logging first, if not found, build it as subdirectory
find_package(HVIS_Logging QUIET)
if(NOT HVIS_Logging_FOUND)
    # Add HVIS_Logging as subdirectory if not found as installed package
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/../HVIS_Logging/CMakeLists.txt")
        # Only add subdirectory if not already added
        if(NOT TARGET HVIS::Logging)
            add_subdirectory("${CMAKE_CURRENT_SOURCE_DIR}/../HVIS_Logging" HVIS_Logging_build)
        endif()
        message(STATUS "Building HVIS_Logging from source")
    else()
        message(FATAL_ERROR "HVIS_Logging not found and source directory not available")
    endif()
else()
    message(STATUS "Found installed HVIS_Logging")
endif()

# Automatically find all source and header files
file(GLOB_RECURSE SOURCES CONFIGURE_DEPENDS "src/*.cpp")
file(GLOB_RECURSE HEADERS CONFIGURE_DEPENDS "include/*.h" "*.h")

# Define the library
add_library(ImageAcquisition SHARED
  ${SOURCES}
  ${HEADERS}
)

# Enable automatic export of all symbols on Windows
set_target_properties(ImageAcquisition PROPERTIES
  WINDOWS_EXPORT_ALL_SYMBOLS ON
)

target_compile_definitions(ImageAcquisition PRIVATE IMAGEACQUISITION_LIBRARY)

# Link against spdlog and HVIS_Logging
target_link_libraries(ImageAcquisition PUBLIC
    spdlog::spdlog
    HVIS::Logging
)
target_include_directories(ImageAcquisition PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/include)

# Include eGrabber libs
include_directories("C:/Program Files/Euresys/eGrabber/include")

# Add the executable
add_executable(ImageAcquisitionApp main.cpp)

# Add dependency to ensure correct build order
add_dependencies(ImageAcquisitionApp ImageAcquisition)

target_link_libraries(ImageAcquisitionApp PRIVATE ImageAcquisition)
target_include_directories(ImageAcquisitionApp PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/include)

# Install targets
install(TARGETS ImageAcquisition
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(TARGETS ImageAcquisitionApp
    RUNTIME DESTINATION bin
)

# Install headers
install(DIRECTORY include/ DESTINATION include)
install(DIRECTORY include/ DESTINATION include FILES_MATCHING PATTERN "*.h")
