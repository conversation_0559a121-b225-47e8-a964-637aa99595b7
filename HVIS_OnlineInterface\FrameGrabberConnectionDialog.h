#ifndef FRAMEGRABBERCONNECTIONDIALOG_H
#define FRAMEGRABBERCONNECTIONDIALOG_H

#include <QDialog>

#include "EuresysFrameGrabber.h"

namespace Ui {
class FrameGrabberConnectionDialog;
}

class FrameGrabberConnectionDialog : public QDialog
{
    Q_OBJECT

public:
    explicit FrameGrabberConnectionDialog(
        std::unique_ptr<HVIS::EuresysFrameGrabber>& t_frameGrabber,
        QWidget *parent = nullptr);
    ~FrameGrabberConnectionDialog();

private slots:
    void on_refreshButton_clicked();

private:
    Ui::FrameGrabberConnectionDialog *ui;

    // Reference to the frame grabber from MainWindow
    std::unique_ptr<HVIS::EuresysFrameGrabber>& m_frameGrabber;

    std::vector<std::string> m_cameraList;
};

#endif // FRAMEGRABBERCONNECTIONDIALOG_H
