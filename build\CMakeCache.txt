# This is the CMakeCache file.
# For build in directory: c:/Users/<USER>/repos/HVIS/build
# It was generated by CMake: C:/mingw64/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build test programs
BUILD_TESTS:BOOL=OFF

//The directory containing a CMake configuration file for Backward.
Backward_DIR:PATH=C:/Users/<USER>/repos/HVIS/build

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/lib.exe

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/MP16 /DWIN32 /D_WINDOWS /GR /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=C:/Users/<USER>/repos/HVIS/build/CMakeFiles/pkgRedirects

CMAKE_GENERATOR_PLATFORM:STRING=x64

CMAKE_GENERATOR_TOOLSET:STRING=v143

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files/HVIS_Suite

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.43.34808/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=HVIS_Suite

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=rc

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//The CMake toolchain file
CMAKE_TOOLCHAIN_FILE:FILEPATH=C:/Users/<USER>/repos/HVIS/build/conan_toolchain.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Value Computed by CMake
HVIS_ImageAcquisition_BINARY_DIR:STATIC=C:/Users/<USER>/repos/HVIS/build/HVIS_ImageAcquisition

//Value Computed by CMake
HVIS_ImageAcquisition_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
HVIS_ImageAcquisition_SOURCE_DIR:STATIC=C:/Users/<USER>/repos/HVIS/HVIS_ImageAcquisition

//Value Computed by CMake
HVIS_Logging_BINARY_DIR:STATIC=C:/Users/<USER>/repos/HVIS/build/HVIS_Logging

//The directory containing a CMake configuration file for HVIS_Logging.
HVIS_Logging_DIR:PATH=C:/Users/<USER>/repos/HVIS/build/HVIS_Logging

//Value Computed by CMake
HVIS_Logging_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
HVIS_Logging_SOURCE_DIR:STATIC=C:/Users/<USER>/repos/HVIS/HVIS_Logging

//Value Computed by CMake
HVIS_Suite_BINARY_DIR:STATIC=C:/Users/<USER>/repos/HVIS/build

//Value Computed by CMake
HVIS_Suite_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
HVIS_Suite_SOURCE_DIR:STATIC=C:/Users/<USER>/repos/HVIS

//Value Computed by CMake
HexcelVisionApp_BINARY_DIR:STATIC=C:/Users/<USER>/repos/HVIS/build/HVIS_OnlineInterface

//Value Computed by CMake
HexcelVisionApp_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
HexcelVisionApp_SOURCE_DIR:STATIC=C:/Users/<USER>/repos/HVIS/HVIS_OnlineInterface

//Additional directories where find(Qt6 ...) host Qt components
// are searched
QT_ADDITIONAL_HOST_PACKAGES_PREFIX_PATH:STRING=

//Additional directories where find(Qt6 ...) components are searched
QT_ADDITIONAL_PACKAGES_PREFIX_PATH:STRING=

//The directory containing a CMake configuration file for Qt6CorePrivate.
Qt6CorePrivate_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6CorePrivate

//The directory containing a CMake configuration file for Qt6CoreTools.
Qt6CoreTools_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6CoreTools

//The directory containing a CMake configuration file for Qt6Core.
Qt6Core_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Core

//The directory containing a CMake configuration file for Qt6EntryPointPrivate.
Qt6EntryPointPrivate_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6EntryPointPrivate

//The directory containing a CMake configuration file for Qt6GuiPrivate.
Qt6GuiPrivate_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6GuiPrivate

//The directory containing a CMake configuration file for Qt6GuiTools.
Qt6GuiTools_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6GuiTools

//The directory containing a CMake configuration file for Qt6Gui.
Qt6Gui_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Gui

//The directory containing a CMake configuration file for Qt6LinguistTools.
Qt6LinguistTools_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6LinguistTools

//The directory containing a CMake configuration file for Qt6WidgetsPrivate.
Qt6WidgetsPrivate_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6WidgetsPrivate

//The directory containing a CMake configuration file for Qt6WidgetsTools.
Qt6WidgetsTools_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6WidgetsTools

//The directory containing a CMake configuration file for Qt6Widgets.
Qt6Widgets_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6Widgets

//The directory containing a CMake configuration file for Qt6ZlibPrivate.
Qt6ZlibPrivate_DIR:PATH=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6ZlibPrivate

//The directory containing a CMake configuration file for Threads.
Threads_DIR:PATH=Threads_DIR-NOTFOUND

//The directory containing a CMake configuration file for Vulkan.
Vulkan_DIR:PATH=Vulkan_DIR-NOTFOUND

//Path to a program.
Vulkan_GLSLANG_VALIDATOR_EXECUTABLE:FILEPATH=Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-NOTFOUND

//Path to a program.
Vulkan_GLSLC_EXECUTABLE:FILEPATH=Vulkan_GLSLC_EXECUTABLE-NOTFOUND

//Path to a file.
Vulkan_INCLUDE_DIR:PATH=Vulkan_INCLUDE_DIR-NOTFOUND

//Path to a library.
Vulkan_LIBRARY:FILEPATH=Vulkan_LIBRARY-NOTFOUND

//Path to a program.
WINDEPLOYQT_EXECUTABLE:FILEPATH=C:/Qt_6/6.9.0/msvc2022_64/bin/windeployqt.exe

//The directory containing a CMake configuration file for WrapAtomic.
WrapAtomic_DIR:PATH=WrapAtomic_DIR-NOTFOUND

//The directory containing a CMake configuration file for WrapVulkanHeaders.
WrapVulkanHeaders_DIR:PATH=WrapVulkanHeaders_DIR-NOTFOUND

//The directory containing a CMake configuration file for fmt.
fmt_DIR:PATH=C:/Users/<USER>/repos/HVIS/build

//The directory containing a CMake configuration file for spdlog.
spdlog_DIR:PATH=C:/Users/<USER>/repos/HVIS/build


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/Users/<USER>/repos/HVIS/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=5
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/mingw64/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/mingw64/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/mingw64/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Professional
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/Users/<USER>/repos/HVIS
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=4
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/mingw64/share/cmake-3.31
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//Details about finding WrapAtomic
FIND_PACKAGE_MESSAGE_DETAILS_WrapAtomic:INTERNAL=[1][v()]
//Test HAVE_STDATOMIC
HAVE_STDATOMIC:INTERNAL=1
//Qt feature: abstractbutton (from target Qt6::Widgets)
QT_FEATURE_abstractbutton:INTERNAL=ON
//Qt feature: abstractslider (from target Qt6::Widgets)
QT_FEATURE_abstractslider:INTERNAL=ON
//Qt feature: accessibility (from target Qt6::Gui)
QT_FEATURE_accessibility:INTERNAL=ON
//Qt feature: accessibility_atspi_bridge (from target Qt6::Gui)
QT_FEATURE_accessibility_atspi_bridge:INTERNAL=OFF
//Qt feature: action (from target Qt6::Gui)
QT_FEATURE_action:INTERNAL=ON
//Qt feature: aesni (from target Qt6::Core)
QT_FEATURE_aesni:INTERNAL=ON
//Qt feature: android_style_assets (from target Qt6::Core)
QT_FEATURE_android_style_assets:INTERNAL=OFF
//Qt feature: animation (from target Qt6::Core)
QT_FEATURE_animation:INTERNAL=ON
//Qt feature: appstore_compliant (from target Qt6::Core)
QT_FEATURE_appstore_compliant:INTERNAL=OFF
//Qt feature: arm_crc32 (from target Qt6::Core)
QT_FEATURE_arm_crc32:INTERNAL=OFF
//Qt feature: arm_crypto (from target Qt6::Core)
QT_FEATURE_arm_crypto:INTERNAL=OFF
//Qt feature: arm_sve (from target Qt6::Core)
QT_FEATURE_arm_sve:INTERNAL=OFF
//Qt feature: avx (from target Qt6::Core)
QT_FEATURE_avx:INTERNAL=ON
//Qt feature: avx2 (from target Qt6::Core)
QT_FEATURE_avx2:INTERNAL=ON
//Qt feature: avx512bw (from target Qt6::Core)
QT_FEATURE_avx512bw:INTERNAL=ON
//Qt feature: avx512cd (from target Qt6::Core)
QT_FEATURE_avx512cd:INTERNAL=ON
//Qt feature: avx512dq (from target Qt6::Core)
QT_FEATURE_avx512dq:INTERNAL=ON
//Qt feature: avx512er (from target Qt6::Core)
QT_FEATURE_avx512er:INTERNAL=ON
//Qt feature: avx512f (from target Qt6::Core)
QT_FEATURE_avx512f:INTERNAL=ON
//Qt feature: avx512ifma (from target Qt6::Core)
QT_FEATURE_avx512ifma:INTERNAL=ON
//Qt feature: avx512pf (from target Qt6::Core)
QT_FEATURE_avx512pf:INTERNAL=ON
//Qt feature: avx512vbmi (from target Qt6::Core)
QT_FEATURE_avx512vbmi:INTERNAL=ON
//Qt feature: avx512vbmi2 (from target Qt6::Core)
QT_FEATURE_avx512vbmi2:INTERNAL=ON
//Qt feature: avx512vl (from target Qt6::Core)
QT_FEATURE_avx512vl:INTERNAL=ON
//Qt feature: backtrace (from target Qt6::Core)
QT_FEATURE_backtrace:INTERNAL=OFF
//Qt feature: broken_threadlocal_dtors (from target Qt6::Core)
QT_FEATURE_broken_threadlocal_dtors:INTERNAL=ON
//Qt feature: buttongroup (from target Qt6::Widgets)
QT_FEATURE_buttongroup:INTERNAL=ON
//Qt feature: calendarwidget (from target Qt6::Widgets)
QT_FEATURE_calendarwidget:INTERNAL=ON
//Qt feature: cborstreamreader (from target Qt6::Core)
QT_FEATURE_cborstreamreader:INTERNAL=ON
//Qt feature: cborstreamwriter (from target Qt6::Core)
QT_FEATURE_cborstreamwriter:INTERNAL=ON
//Qt feature: checkbox (from target Qt6::Widgets)
QT_FEATURE_checkbox:INTERNAL=ON
//Qt feature: clipboard (from target Qt6::Gui)
QT_FEATURE_clipboard:INTERNAL=ON
//Qt feature: clock_gettime (from target Qt6::Core)
QT_FEATURE_clock_gettime:INTERNAL=OFF
//Qt feature: clock_monotonic (from target Qt6::Core)
QT_FEATURE_clock_monotonic:INTERNAL=OFF
//Qt feature: colordialog (from target Qt6::Widgets)
QT_FEATURE_colordialog:INTERNAL=ON
//Qt feature: colornames (from target Qt6::Gui)
QT_FEATURE_colornames:INTERNAL=ON
//Qt feature: columnview (from target Qt6::Widgets)
QT_FEATURE_columnview:INTERNAL=ON
//Qt feature: combobox (from target Qt6::Widgets)
QT_FEATURE_combobox:INTERNAL=ON
//Qt feature: commandlineparser (from target Qt6::Core)
QT_FEATURE_commandlineparser:INTERNAL=ON
//Qt feature: commandlinkbutton (from target Qt6::Widgets)
QT_FEATURE_commandlinkbutton:INTERNAL=ON
//Qt feature: completer (from target Qt6::Widgets)
QT_FEATURE_completer:INTERNAL=ON
//Qt feature: concatenatetablesproxymodel (from target Qt6::Core)
QT_FEATURE_concatenatetablesproxymodel:INTERNAL=ON
//Qt feature: concurrent (from target Qt6::Core)
QT_FEATURE_concurrent:INTERNAL=ON
//Qt feature: contextmenu (from target Qt6::Widgets)
QT_FEATURE_contextmenu:INTERNAL=ON
//Qt feature: cpp_winrt (from target Qt6::Core)
QT_FEATURE_cpp_winrt:INTERNAL=ON
//Qt feature: cross_compile (from target Qt6::Core)
QT_FEATURE_cross_compile:INTERNAL=OFF
//Qt feature: cssparser (from target Qt6::Gui)
QT_FEATURE_cssparser:INTERNAL=ON
//Qt feature: ctf (from target Qt6::Core)
QT_FEATURE_ctf:INTERNAL=OFF
//Qt feature: cursor (from target Qt6::Gui)
QT_FEATURE_cursor:INTERNAL=ON
//Qt feature: cxx11_future (from target Qt6::Core)
QT_FEATURE_cxx11_future:INTERNAL=ON
//Qt feature: cxx17_filesystem (from target Qt6::Core)
QT_FEATURE_cxx17_filesystem:INTERNAL=ON
//Qt feature: cxx20 (from target Qt6::Core)
QT_FEATURE_cxx20:INTERNAL=OFF
//Qt feature: cxx23_stacktrace (from target Qt6::Core)
QT_FEATURE_cxx23_stacktrace:INTERNAL=OFF
//Qt feature: cxx2a (from target Qt6::Core)
QT_FEATURE_cxx2a:INTERNAL=OFF
//Qt feature: cxx2b (from target Qt6::Core)
QT_FEATURE_cxx2b:INTERNAL=OFF
//Qt feature: datawidgetmapper (from target Qt6::Widgets)
QT_FEATURE_datawidgetmapper:INTERNAL=ON
//Qt feature: datestring (from target Qt6::Core)
QT_FEATURE_datestring:INTERNAL=ON
//Qt feature: datetimeedit (from target Qt6::Widgets)
QT_FEATURE_datetimeedit:INTERNAL=ON
//Qt feature: datetimeparser (from target Qt6::Core)
QT_FEATURE_datetimeparser:INTERNAL=ON
//Qt feature: dbus (from target Qt6::Core)
QT_FEATURE_dbus:INTERNAL=ON
//Qt feature: dbus_linked (from target Qt6::Core)
QT_FEATURE_dbus_linked:INTERNAL=OFF
//Qt feature: debug (from target Qt6::Core)
QT_FEATURE_debug:INTERNAL=ON
//Qt feature: debug_and_release (from target Qt6::Core)
QT_FEATURE_debug_and_release:INTERNAL=ON
//Qt feature: desktopservices (from target Qt6::Gui)
QT_FEATURE_desktopservices:INTERNAL=ON
//Qt feature: developer_build (from target Qt6::Core)
QT_FEATURE_developer_build:INTERNAL=OFF
//Qt feature: dial (from target Qt6::Widgets)
QT_FEATURE_dial:INTERNAL=ON
//Qt feature: dialog (from target Qt6::Widgets)
QT_FEATURE_dialog:INTERNAL=ON
//Qt feature: dialogbuttonbox (from target Qt6::Widgets)
QT_FEATURE_dialogbuttonbox:INTERNAL=ON
//Qt feature: direct2d (from target Qt6::Gui)
QT_FEATURE_direct2d:INTERNAL=ON
//Qt feature: direct2d1_1 (from target Qt6::Gui)
QT_FEATURE_direct2d1_1:INTERNAL=ON
//Qt feature: directfb (from target Qt6::Gui)
QT_FEATURE_directfb:INTERNAL=OFF
//Qt feature: directwrite (from target Qt6::Gui)
QT_FEATURE_directwrite:INTERNAL=ON
//Qt feature: directwrite3 (from target Qt6::Gui)
QT_FEATURE_directwrite3:INTERNAL=ON
//Qt feature: directwritecolrv1 (from target Qt6::Gui)
QT_FEATURE_directwritecolrv1:INTERNAL=ON
//Qt feature: dladdr (from target Qt6::Core)
QT_FEATURE_dladdr:INTERNAL=OFF
//Qt feature: dlopen (from target Qt6::Core)
QT_FEATURE_dlopen:INTERNAL=OFF
//Qt feature: doc_snippets (from target Qt6::Core)
QT_FEATURE_doc_snippets:INTERNAL=OFF
//Qt feature: dockwidget (from target Qt6::Widgets)
QT_FEATURE_dockwidget:INTERNAL=ON
//Qt feature: doubleconversion (from target Qt6::Core)
QT_FEATURE_doubleconversion:INTERNAL=ON
//Qt feature: draganddrop (from target Qt6::Gui)
QT_FEATURE_draganddrop:INTERNAL=ON
//Qt feature: drm_atomic (from target Qt6::Gui)
QT_FEATURE_drm_atomic:INTERNAL=OFF
//Qt feature: dynamicgl (from target Qt6::Gui)
QT_FEATURE_dynamicgl:INTERNAL=ON
//Qt feature: easingcurve (from target Qt6::Core)
QT_FEATURE_easingcurve:INTERNAL=ON
//Qt feature: effects (from target Qt6::Widgets)
QT_FEATURE_effects:INTERNAL=ON
//Qt feature: egl (from target Qt6::Gui)
QT_FEATURE_egl:INTERNAL=OFF
//Qt feature: egl_x11 (from target Qt6::Gui)
QT_FEATURE_egl_x11:INTERNAL=OFF
//Qt feature: eglfs (from target Qt6::Gui)
QT_FEATURE_eglfs:INTERNAL=OFF
//Qt feature: eglfs_brcm (from target Qt6::Gui)
QT_FEATURE_eglfs_brcm:INTERNAL=OFF
//Qt feature: eglfs_egldevice (from target Qt6::Gui)
QT_FEATURE_eglfs_egldevice:INTERNAL=OFF
//Qt feature: eglfs_gbm (from target Qt6::Gui)
QT_FEATURE_eglfs_gbm:INTERNAL=OFF
//Qt feature: eglfs_mali (from target Qt6::Gui)
QT_FEATURE_eglfs_mali:INTERNAL=OFF
//Qt feature: eglfs_openwfd (from target Qt6::Gui)
QT_FEATURE_eglfs_openwfd:INTERNAL=OFF
//Qt feature: eglfs_rcar (from target Qt6::Gui)
QT_FEATURE_eglfs_rcar:INTERNAL=OFF
//Qt feature: eglfs_viv (from target Qt6::Gui)
QT_FEATURE_eglfs_viv:INTERNAL=OFF
//Qt feature: eglfs_viv_wl (from target Qt6::Gui)
QT_FEATURE_eglfs_viv_wl:INTERNAL=OFF
//Qt feature: eglfs_vsp2 (from target Qt6::Gui)
QT_FEATURE_eglfs_vsp2:INTERNAL=OFF
//Qt feature: eglfs_x11 (from target Qt6::Gui)
QT_FEATURE_eglfs_x11:INTERNAL=OFF
//Qt feature: elf_private_full_version (from target Qt6::Core)
QT_FEATURE_elf_private_full_version:INTERNAL=OFF
//Qt feature: emojisegmenter (from target Qt6::Gui)
QT_FEATURE_emojisegmenter:INTERNAL=ON
//Qt feature: errormessage (from target Qt6::Widgets)
QT_FEATURE_errormessage:INTERNAL=ON
//Qt feature: etw (from target Qt6::Core)
QT_FEATURE_etw:INTERNAL=OFF
//Qt feature: evdev (from target Qt6::Gui)
QT_FEATURE_evdev:INTERNAL=OFF
//Qt feature: f16c (from target Qt6::Core)
QT_FEATURE_f16c:INTERNAL=ON
//Qt feature: filedialog (from target Qt6::Widgets)
QT_FEATURE_filedialog:INTERNAL=ON
//Qt feature: filesystemiterator (from target Qt6::Core)
QT_FEATURE_filesystemiterator:INTERNAL=ON
//Qt feature: filesystemmodel (from target Qt6::Gui)
QT_FEATURE_filesystemmodel:INTERNAL=ON
//Qt feature: filesystemwatcher (from target Qt6::Core)
QT_FEATURE_filesystemwatcher:INTERNAL=ON
//Qt feature: fontcombobox (from target Qt6::Widgets)
QT_FEATURE_fontcombobox:INTERNAL=ON
//Qt feature: fontconfig (from target Qt6::Gui)
QT_FEATURE_fontconfig:INTERNAL=OFF
//Qt feature: fontdialog (from target Qt6::Widgets)
QT_FEATURE_fontdialog:INTERNAL=ON
//Qt feature: force_asserts (from target Qt6::Core)
QT_FEATURE_force_asserts:INTERNAL=OFF
//Qt feature: force_bundled_libs (from target Qt6::Core)
QT_FEATURE_force_bundled_libs:INTERNAL=OFF
//Qt feature: force_debug_info (from target Qt6::Core)
QT_FEATURE_force_debug_info:INTERNAL=ON
//Qt feature: force_system_libs (from target Qt6::Core)
QT_FEATURE_force_system_libs:INTERNAL=OFF
//Qt feature: forkfd_pidfd (from target Qt6::Core)
QT_FEATURE_forkfd_pidfd:INTERNAL=OFF
//Qt feature: formlayout (from target Qt6::Widgets)
QT_FEATURE_formlayout:INTERNAL=ON
//Qt feature: framework (from target Qt6::Core)
QT_FEATURE_framework:INTERNAL=OFF
//Qt feature: freetype (from target Qt6::Gui)
QT_FEATURE_freetype:INTERNAL=ON
//Qt feature: fscompleter (from target Qt6::Widgets)
QT_FEATURE_fscompleter:INTERNAL=ON
//Qt feature: futimens (from target Qt6::Core)
QT_FEATURE_futimens:INTERNAL=OFF
//Qt feature: future (from target Qt6::Core)
QT_FEATURE_future:INTERNAL=ON
//Qt feature: gc_binaries (from target Qt6::Core)
QT_FEATURE_gc_binaries:INTERNAL=OFF
//Qt feature: gestures (from target Qt6::Core)
QT_FEATURE_gestures:INTERNAL=ON
//Qt feature: getauxval (from target Qt6::Core)
QT_FEATURE_getauxval:INTERNAL=OFF
//Qt feature: getentropy (from target Qt6::Core)
QT_FEATURE_getentropy:INTERNAL=OFF
//Qt feature: gif (from target Qt6::Gui)
QT_FEATURE_gif:INTERNAL=ON
//Qt feature: glib (from target Qt6::Core)
QT_FEATURE_glib:INTERNAL=OFF
//Qt feature: glibc_fortify_source (from target Qt6::Core)
QT_FEATURE_glibc_fortify_source:INTERNAL=OFF
//Qt feature: graphicseffect (from target Qt6::Widgets)
QT_FEATURE_graphicseffect:INTERNAL=ON
//Qt feature: graphicsframecapture (from target Qt6::Gui)
QT_FEATURE_graphicsframecapture:INTERNAL=OFF
//Qt feature: graphicsview (from target Qt6::Widgets)
QT_FEATURE_graphicsview:INTERNAL=ON
//Qt feature: groupbox (from target Qt6::Widgets)
QT_FEATURE_groupbox:INTERNAL=ON
//Qt feature: gtk3 (from target Qt6::Widgets)
QT_FEATURE_gtk3:INTERNAL=OFF
//Qt feature: gui (from target Qt6::Core)
QT_FEATURE_gui:INTERNAL=ON
//Qt feature: harfbuzz (from target Qt6::Gui)
QT_FEATURE_harfbuzz:INTERNAL=ON
//Qt feature: highdpiscaling (from target Qt6::Gui)
QT_FEATURE_highdpiscaling:INTERNAL=ON
//Qt feature: hijricalendar (from target Qt6::Core)
QT_FEATURE_hijricalendar:INTERNAL=ON
//Qt feature: ico (from target Qt6::Gui)
QT_FEATURE_ico:INTERNAL=ON
//Qt feature: icu (from target Qt6::Core)
QT_FEATURE_icu:INTERNAL=OFF
//Qt feature: identityproxymodel (from target Qt6::Core)
QT_FEATURE_identityproxymodel:INTERNAL=ON
//Qt feature: im (from target Qt6::Gui)
QT_FEATURE_im:INTERNAL=ON
//Qt feature: image_heuristic_mask (from target Qt6::Gui)
QT_FEATURE_image_heuristic_mask:INTERNAL=ON
//Qt feature: image_text (from target Qt6::Gui)
QT_FEATURE_image_text:INTERNAL=ON
//Qt feature: imageformat_bmp (from target Qt6::Gui)
QT_FEATURE_imageformat_bmp:INTERNAL=ON
//Qt feature: imageformat_jpeg (from target Qt6::Gui)
QT_FEATURE_imageformat_jpeg:INTERNAL=ON
//Qt feature: imageformat_png (from target Qt6::Gui)
QT_FEATURE_imageformat_png:INTERNAL=ON
//Qt feature: imageformat_ppm (from target Qt6::Gui)
QT_FEATURE_imageformat_ppm:INTERNAL=ON
//Qt feature: imageformat_xbm (from target Qt6::Gui)
QT_FEATURE_imageformat_xbm:INTERNAL=ON
//Qt feature: imageformat_xpm (from target Qt6::Gui)
QT_FEATURE_imageformat_xpm:INTERNAL=ON
//Qt feature: imageformatplugin (from target Qt6::Gui)
QT_FEATURE_imageformatplugin:INTERNAL=ON
//Qt feature: imageio_text_loading (from target Qt6::Gui)
QT_FEATURE_imageio_text_loading:INTERNAL=ON
//Qt feature: inotify (from target Qt6::Core)
QT_FEATURE_inotify:INTERNAL=OFF
//Qt feature: inputdialog (from target Qt6::Widgets)
QT_FEATURE_inputdialog:INTERNAL=ON
//Qt feature: integrityfb (from target Qt6::Gui)
QT_FEATURE_integrityfb:INTERNAL=OFF
//Qt feature: integrityhid (from target Qt6::Gui)
QT_FEATURE_integrityhid:INTERNAL=OFF
//Qt feature: intelcet (from target Qt6::Core)
QT_FEATURE_intelcet:INTERNAL=ON
//Qt feature: islamiccivilcalendar (from target Qt6::Core)
QT_FEATURE_islamiccivilcalendar:INTERNAL=ON
//Qt feature: itemmodel (from target Qt6::Core)
QT_FEATURE_itemmodel:INTERNAL=ON
//Qt feature: itemviews (from target Qt6::Widgets)
QT_FEATURE_itemviews:INTERNAL=ON
//Qt feature: jalalicalendar (from target Qt6::Core)
QT_FEATURE_jalalicalendar:INTERNAL=ON
//Qt feature: journald (from target Qt6::Core)
QT_FEATURE_journald:INTERNAL=OFF
//Qt feature: jpeg (from target Qt6::Gui)
QT_FEATURE_jpeg:INTERNAL=ON
//Qt feature: keysequenceedit (from target Qt6::Widgets)
QT_FEATURE_keysequenceedit:INTERNAL=ON
//Qt feature: kms (from target Qt6::Gui)
QT_FEATURE_kms:INTERNAL=OFF
//Qt feature: label (from target Qt6::Widgets)
QT_FEATURE_label:INTERNAL=ON
//Qt feature: largefile (from target Qt6::Core)
QT_FEATURE_largefile:INTERNAL=ON
//Qt feature: lasx (from target Qt6::Core)
QT_FEATURE_lasx:INTERNAL=OFF
//Qt feature: lcdnumber (from target Qt6::Widgets)
QT_FEATURE_lcdnumber:INTERNAL=ON
//Qt feature: libcpp_hardening (from target Qt6::Core)
QT_FEATURE_libcpp_hardening:INTERNAL=OFF
//Qt feature: libinput (from target Qt6::Gui)
QT_FEATURE_libinput:INTERNAL=OFF
//Qt feature: libinput_axis_api (from target Qt6::Gui)
QT_FEATURE_libinput_axis_api:INTERNAL=OFF
//Qt feature: libinput_hires_wheel_support (from target Qt6::Gui)
QT_FEATURE_libinput_hires_wheel_support:INTERNAL=OFF
//Qt feature: library (from target Qt6::Core)
QT_FEATURE_library:INTERNAL=ON
//Qt feature: libstdcpp_assertions (from target Qt6::Core)
QT_FEATURE_libstdcpp_assertions:INTERNAL=OFF
//Qt feature: libudev (from target Qt6::Core)
QT_FEATURE_libudev:INTERNAL=OFF
//Qt feature: lineedit (from target Qt6::Widgets)
QT_FEATURE_lineedit:INTERNAL=ON
//Qt feature: linkat (from target Qt6::Core)
QT_FEATURE_linkat:INTERNAL=OFF
//Qt feature: linuxfb (from target Qt6::Gui)
QT_FEATURE_linuxfb:INTERNAL=OFF
//Qt feature: listview (from target Qt6::Widgets)
QT_FEATURE_listview:INTERNAL=ON
//Qt feature: listwidget (from target Qt6::Widgets)
QT_FEATURE_listwidget:INTERNAL=ON
//Qt feature: localtime_r (from target Qt6::Core)
QT_FEATURE_localtime_r:INTERNAL=OFF
//Qt feature: localtime_s (from target Qt6::Core)
QT_FEATURE_localtime_s:INTERNAL=ON
//Qt feature: lsx (from target Qt6::Core)
QT_FEATURE_lsx:INTERNAL=OFF
//Qt feature: lttng (from target Qt6::Core)
QT_FEATURE_lttng:INTERNAL=OFF
//Qt feature: mainwindow (from target Qt6::Widgets)
QT_FEATURE_mainwindow:INTERNAL=ON
//Qt feature: mdiarea (from target Qt6::Widgets)
QT_FEATURE_mdiarea:INTERNAL=ON
//Qt feature: memmem (from target Qt6::Core)
QT_FEATURE_memmem:INTERNAL=OFF
//Qt feature: memrchr (from target Qt6::Core)
QT_FEATURE_memrchr:INTERNAL=OFF
//Qt feature: menu (from target Qt6::Widgets)
QT_FEATURE_menu:INTERNAL=ON
//Qt feature: menubar (from target Qt6::Widgets)
QT_FEATURE_menubar:INTERNAL=ON
//Qt feature: messagebox (from target Qt6::Widgets)
QT_FEATURE_messagebox:INTERNAL=ON
//Qt feature: metal (from target Qt6::Gui)
QT_FEATURE_metal:INTERNAL=OFF
//Qt feature: mimetype (from target Qt6::Core)
QT_FEATURE_mimetype:INTERNAL=ON
//Qt feature: mimetype_database (from target Qt6::Core)
QT_FEATURE_mimetype_database:INTERNAL=ON
//Qt feature: mips_dsp (from target Qt6::Core)
QT_FEATURE_mips_dsp:INTERNAL=OFF
//Qt feature: mips_dspr2 (from target Qt6::Core)
QT_FEATURE_mips_dspr2:INTERNAL=OFF
//Qt feature: movie (from target Qt6::Gui)
QT_FEATURE_movie:INTERNAL=ON
//Qt feature: msvc_obj_debug_info (from target Qt6::Core)
QT_FEATURE_msvc_obj_debug_info:INTERNAL=ON
//Qt feature: mtdev (from target Qt6::Gui)
QT_FEATURE_mtdev:INTERNAL=OFF
//Qt feature: multiprocess (from target Qt6::Gui)
QT_FEATURE_multiprocess:INTERNAL=ON
//Qt feature: neon (from target Qt6::Core)
QT_FEATURE_neon:INTERNAL=OFF
//Qt feature: network (from target Qt6::Core)
QT_FEATURE_network:INTERNAL=ON
//Qt feature: no_direct_extern_access (from target Qt6::Core)
QT_FEATURE_no_direct_extern_access:INTERNAL=OFF
//Qt feature: no_pkg_config (from target Qt6::Core)
QT_FEATURE_no_pkg_config:INTERNAL=ON
//Qt feature: opengl (from target Qt6::Gui)
QT_FEATURE_opengl:INTERNAL=ON
//Qt feature: opengles2 (from target Qt6::Gui)
QT_FEATURE_opengles2:INTERNAL=OFF
//Qt feature: opengles3 (from target Qt6::Gui)
QT_FEATURE_opengles3:INTERNAL=OFF
//Qt feature: opengles31 (from target Qt6::Gui)
QT_FEATURE_opengles31:INTERNAL=OFF
//Qt feature: opengles32 (from target Qt6::Gui)
QT_FEATURE_opengles32:INTERNAL=OFF
//Qt feature: openssl (from target Qt6::Core)
QT_FEATURE_openssl:INTERNAL=ON
//Qt feature: openssl_hash (from target Qt6::Core)
QT_FEATURE_openssl_hash:INTERNAL=OFF
//Qt feature: openssl_linked (from target Qt6::Core)
QT_FEATURE_openssl_linked:INTERNAL=OFF
//Qt feature: opensslv11 (from target Qt6::Core)
QT_FEATURE_opensslv11:INTERNAL=OFF
//Qt feature: opensslv30 (from target Qt6::Core)
QT_FEATURE_opensslv30:INTERNAL=ON
//Qt feature: openvg (from target Qt6::Gui)
QT_FEATURE_openvg:INTERNAL=OFF
//Qt feature: pcre2 (from target Qt6::Core)
QT_FEATURE_pcre2:INTERNAL=ON
//Qt feature: pdf (from target Qt6::Gui)
QT_FEATURE_pdf:INTERNAL=ON
//Qt feature: permissions (from target Qt6::Core)
QT_FEATURE_permissions:INTERNAL=ON
//Qt feature: picture (from target Qt6::Gui)
QT_FEATURE_picture:INTERNAL=ON
//Qt feature: pkg_config (from target Qt6::Core)
QT_FEATURE_pkg_config:INTERNAL=OFF
//Qt feature: plugin_manifest (from target Qt6::Core)
QT_FEATURE_plugin_manifest:INTERNAL=ON
//Qt feature: png (from target Qt6::Gui)
QT_FEATURE_png:INTERNAL=ON
//Qt feature: poll_exit_on_error (from target Qt6::Core)
QT_FEATURE_poll_exit_on_error:INTERNAL=OFF
//Qt feature: poll_poll (from target Qt6::Core)
QT_FEATURE_poll_poll:INTERNAL=OFF
//Qt feature: poll_pollts (from target Qt6::Core)
QT_FEATURE_poll_pollts:INTERNAL=OFF
//Qt feature: poll_ppoll (from target Qt6::Core)
QT_FEATURE_poll_ppoll:INTERNAL=OFF
//Qt feature: poll_select (from target Qt6::Core)
QT_FEATURE_poll_select:INTERNAL=OFF
//Qt feature: posix_fallocate (from target Qt6::Core)
QT_FEATURE_posix_fallocate:INTERNAL=OFF
//Qt feature: posix_sem (from target Qt6::Core)
QT_FEATURE_posix_sem:INTERNAL=OFF
//Qt feature: posix_shm (from target Qt6::Core)
QT_FEATURE_posix_shm:INTERNAL=OFF
//Qt feature: precompile_header (from target Qt6::Core)
QT_FEATURE_precompile_header:INTERNAL=ON
//Qt feature: printsupport (from target Qt6::Core)
QT_FEATURE_printsupport:INTERNAL=ON
//Qt feature: private_tests (from target Qt6::Core)
QT_FEATURE_private_tests:INTERNAL=OFF
//Qt feature: process (from target Qt6::Core)
QT_FEATURE_process:INTERNAL=ON
//Qt feature: processenvironment (from target Qt6::Core)
QT_FEATURE_processenvironment:INTERNAL=ON
//Qt feature: progressbar (from target Qt6::Widgets)
QT_FEATURE_progressbar:INTERNAL=ON
//Qt feature: progressdialog (from target Qt6::Widgets)
QT_FEATURE_progressdialog:INTERNAL=ON
//Qt feature: proxymodel (from target Qt6::Core)
QT_FEATURE_proxymodel:INTERNAL=ON
//Qt feature: pthread_clockjoin (from target Qt6::Core)
QT_FEATURE_pthread_clockjoin:INTERNAL=OFF
//Qt feature: pthread_condattr_setclock (from target Qt6::Core)
QT_FEATURE_pthread_condattr_setclock:INTERNAL=OFF
//Qt feature: pthread_timedjoin (from target Qt6::Core)
QT_FEATURE_pthread_timedjoin:INTERNAL=OFF
//Qt feature: pushbutton (from target Qt6::Widgets)
QT_FEATURE_pushbutton:INTERNAL=ON
//Qt feature: qqnx_imf (from target Qt6::Gui)
QT_FEATURE_qqnx_imf:INTERNAL=OFF
//Qt feature: qqnx_pps (from target Qt6::Core)
QT_FEATURE_qqnx_pps:INTERNAL=OFF
//Qt feature: qtgui_threadpool (from target Qt6::Gui)
QT_FEATURE_qtgui_threadpool:INTERNAL=ON
//Qt feature: radiobutton (from target Qt6::Widgets)
QT_FEATURE_radiobutton:INTERNAL=ON
//Qt feature: raster_64bit (from target Qt6::Gui)
QT_FEATURE_raster_64bit:INTERNAL=ON
//Qt feature: raster_fp (from target Qt6::Gui)
QT_FEATURE_raster_fp:INTERNAL=ON
//Qt feature: rdrnd (from target Qt6::Core)
QT_FEATURE_rdrnd:INTERNAL=ON
//Qt feature: rdseed (from target Qt6::Core)
QT_FEATURE_rdseed:INTERNAL=ON
//Qt feature: reduce_exports (from target Qt6::Core)
QT_FEATURE_reduce_exports:INTERNAL=OFF
//Qt feature: reduce_relocations (from target Qt6::Core)
QT_FEATURE_reduce_relocations:INTERNAL=OFF
//Qt feature: regularexpression (from target Qt6::Core)
QT_FEATURE_regularexpression:INTERNAL=ON
//Qt feature: relocatable (from target Qt6::Core)
QT_FEATURE_relocatable:INTERNAL=ON
//Qt feature: relro_now_linker (from target Qt6::Core)
QT_FEATURE_relro_now_linker:INTERNAL=OFF
//Qt feature: renameat2 (from target Qt6::Core)
QT_FEATURE_renameat2:INTERNAL=OFF
//Qt feature: resizehandler (from target Qt6::Widgets)
QT_FEATURE_resizehandler:INTERNAL=ON
//Qt feature: rpath (from target Qt6::Core)
QT_FEATURE_rpath:INTERNAL=OFF
//Qt feature: rubberband (from target Qt6::Widgets)
QT_FEATURE_rubberband:INTERNAL=ON
//Qt feature: scrollarea (from target Qt6::Widgets)
QT_FEATURE_scrollarea:INTERNAL=ON
//Qt feature: scrollbar (from target Qt6::Widgets)
QT_FEATURE_scrollbar:INTERNAL=ON
//Qt feature: scroller (from target Qt6::Widgets)
QT_FEATURE_scroller:INTERNAL=ON
//Qt feature: separate_debug_info (from target Qt6::Core)
QT_FEATURE_separate_debug_info:INTERNAL=OFF
//Qt feature: sessionmanager (from target Qt6::Gui)
QT_FEATURE_sessionmanager:INTERNAL=ON
//Qt feature: settings (from target Qt6::Core)
QT_FEATURE_settings:INTERNAL=ON
//Qt feature: sha3_fast (from target Qt6::Core)
QT_FEATURE_sha3_fast:INTERNAL=ON
//Qt feature: shani (from target Qt6::Core)
QT_FEATURE_shani:INTERNAL=ON
//Qt feature: shared (from target Qt6::Core)
QT_FEATURE_shared:INTERNAL=ON
//Qt feature: sharedmemory (from target Qt6::Core)
QT_FEATURE_sharedmemory:INTERNAL=ON
//Qt feature: shortcut (from target Qt6::Core)
QT_FEATURE_shortcut:INTERNAL=ON
//Qt feature: signaling_nan (from target Qt6::Core)
QT_FEATURE_signaling_nan:INTERNAL=ON
//Qt feature: simulator_and_device (from target Qt6::Core)
QT_FEATURE_simulator_and_device:INTERNAL=OFF
//Qt feature: sizegrip (from target Qt6::Widgets)
QT_FEATURE_sizegrip:INTERNAL=ON
//Qt feature: slider (from target Qt6::Widgets)
QT_FEATURE_slider:INTERNAL=ON
//Qt feature: slog2 (from target Qt6::Core)
QT_FEATURE_slog2:INTERNAL=OFF
//Qt feature: sortfilterproxymodel (from target Qt6::Core)
QT_FEATURE_sortfilterproxymodel:INTERNAL=ON
//Qt feature: spinbox (from target Qt6::Widgets)
QT_FEATURE_spinbox:INTERNAL=ON
//Qt feature: splashscreen (from target Qt6::Widgets)
QT_FEATURE_splashscreen:INTERNAL=ON
//Qt feature: splitter (from target Qt6::Widgets)
QT_FEATURE_splitter:INTERNAL=ON
//Qt feature: sql (from target Qt6::Core)
QT_FEATURE_sql:INTERNAL=ON
//Qt feature: sse2 (from target Qt6::Core)
QT_FEATURE_sse2:INTERNAL=ON
//Qt feature: sse3 (from target Qt6::Core)
QT_FEATURE_sse3:INTERNAL=ON
//Qt feature: sse4_1 (from target Qt6::Core)
QT_FEATURE_sse4_1:INTERNAL=ON
//Qt feature: sse4_2 (from target Qt6::Core)
QT_FEATURE_sse4_2:INTERNAL=ON
//Qt feature: ssse3 (from target Qt6::Core)
QT_FEATURE_ssse3:INTERNAL=ON
//Qt feature: stack_clash_protection (from target Qt6::Core)
QT_FEATURE_stack_clash_protection:INTERNAL=OFF
//Qt feature: stack_protector (from target Qt6::Core)
QT_FEATURE_stack_protector:INTERNAL=OFF
//Qt feature: stackedwidget (from target Qt6::Widgets)
QT_FEATURE_stackedwidget:INTERNAL=ON
//Qt feature: standarditemmodel (from target Qt6::Gui)
QT_FEATURE_standarditemmodel:INTERNAL=ON
//Qt feature: static (from target Qt6::Core)
QT_FEATURE_static:INTERNAL=OFF
//Qt feature: statusbar (from target Qt6::Widgets)
QT_FEATURE_statusbar:INTERNAL=ON
//Qt feature: statustip (from target Qt6::Widgets)
QT_FEATURE_statustip:INTERNAL=ON
//Qt feature: std_atomic64 (from target Qt6::Core)
QT_FEATURE_std_atomic64:INTERNAL=ON
//Qt feature: stdlib_libcpp (from target Qt6::Core)
QT_FEATURE_stdlib_libcpp:INTERNAL=OFF
//Qt feature: stringlistmodel (from target Qt6::Core)
QT_FEATURE_stringlistmodel:INTERNAL=ON
//Qt feature: style_android (from target Qt6::Widgets)
QT_FEATURE_style_android:INTERNAL=OFF
//Qt feature: style_fusion (from target Qt6::Widgets)
QT_FEATURE_style_fusion:INTERNAL=ON
//Qt feature: style_mac (from target Qt6::Widgets)
QT_FEATURE_style_mac:INTERNAL=OFF
//Qt feature: style_stylesheet (from target Qt6::Widgets)
QT_FEATURE_style_stylesheet:INTERNAL=ON
//Qt feature: style_windows (from target Qt6::Widgets)
QT_FEATURE_style_windows:INTERNAL=ON
//Qt feature: style_windows11 (from target Qt6::Widgets)
QT_FEATURE_style_windows11:INTERNAL=ON
//Qt feature: style_windowsvista (from target Qt6::Widgets)
QT_FEATURE_style_windowsvista:INTERNAL=ON
//Qt feature: syntaxhighlighter (from target Qt6::Widgets)
QT_FEATURE_syntaxhighlighter:INTERNAL=ON
//Qt feature: syslog (from target Qt6::Core)
QT_FEATURE_syslog:INTERNAL=OFF
//Qt feature: system_doubleconversion (from target Qt6::Core)
QT_FEATURE_system_doubleconversion:INTERNAL=OFF
//Qt feature: system_freetype (from target Qt6::Gui)
QT_FEATURE_system_freetype:INTERNAL=OFF
//Qt feature: system_harfbuzz (from target Qt6::Gui)
QT_FEATURE_system_harfbuzz:INTERNAL=OFF
//Qt feature: system_jpeg (from target Qt6::Gui)
QT_FEATURE_system_jpeg:INTERNAL=OFF
//Qt feature: system_libb2 (from target Qt6::Core)
QT_FEATURE_system_libb2:INTERNAL=OFF
//Qt feature: system_pcre2 (from target Qt6::Core)
QT_FEATURE_system_pcre2:INTERNAL=OFF
//Qt feature: system_png (from target Qt6::Gui)
QT_FEATURE_system_png:INTERNAL=OFF
//Qt feature: system_textmarkdownreader (from target Qt6::Gui)
QT_FEATURE_system_textmarkdownreader:INTERNAL=OFF
//Qt feature: system_xcb_xinput (from target Qt6::Gui)
QT_FEATURE_system_xcb_xinput:INTERNAL=OFF
//Qt feature: system_zlib (from target Qt6::Core)
QT_FEATURE_system_zlib:INTERNAL=OFF
//Qt feature: systemsemaphore (from target Qt6::Core)
QT_FEATURE_systemsemaphore:INTERNAL=ON
//Qt feature: systemtrayicon (from target Qt6::Gui)
QT_FEATURE_systemtrayicon:INTERNAL=ON
//Qt feature: sysv_sem (from target Qt6::Core)
QT_FEATURE_sysv_sem:INTERNAL=OFF
//Qt feature: sysv_shm (from target Qt6::Core)
QT_FEATURE_sysv_shm:INTERNAL=OFF
//Qt feature: tabbar (from target Qt6::Widgets)
QT_FEATURE_tabbar:INTERNAL=ON
//Qt feature: tabletevent (from target Qt6::Gui)
QT_FEATURE_tabletevent:INTERNAL=ON
//Qt feature: tableview (from target Qt6::Widgets)
QT_FEATURE_tableview:INTERNAL=ON
//Qt feature: tablewidget (from target Qt6::Widgets)
QT_FEATURE_tablewidget:INTERNAL=ON
//Qt feature: tabwidget (from target Qt6::Widgets)
QT_FEATURE_tabwidget:INTERNAL=ON
//Qt feature: temporaryfile (from target Qt6::Core)
QT_FEATURE_temporaryfile:INTERNAL=ON
//Qt feature: test_gui (from target Qt6::Core)
QT_FEATURE_test_gui:INTERNAL=ON
//Qt feature: testlib (from target Qt6::Core)
QT_FEATURE_testlib:INTERNAL=ON
//Qt feature: textbrowser (from target Qt6::Widgets)
QT_FEATURE_textbrowser:INTERNAL=ON
//Qt feature: textdate (from target Qt6::Core)
QT_FEATURE_textdate:INTERNAL=ON
//Qt feature: textedit (from target Qt6::Widgets)
QT_FEATURE_textedit:INTERNAL=ON
//Qt feature: texthtmlparser (from target Qt6::Gui)
QT_FEATURE_texthtmlparser:INTERNAL=ON
//Qt feature: textmarkdownreader (from target Qt6::Gui)
QT_FEATURE_textmarkdownreader:INTERNAL=ON
//Qt feature: textmarkdownwriter (from target Qt6::Gui)
QT_FEATURE_textmarkdownwriter:INTERNAL=ON
//Qt feature: textodfwriter (from target Qt6::Gui)
QT_FEATURE_textodfwriter:INTERNAL=ON
//Qt feature: thread (from target Qt6::Core)
QT_FEATURE_thread:INTERNAL=ON
//Qt feature: timezone (from target Qt6::Core)
QT_FEATURE_timezone:INTERNAL=ON
//Qt feature: timezone_locale (from target Qt6::Core)
QT_FEATURE_timezone_locale:INTERNAL=ON
//Qt feature: timezone_tzdb (from target Qt6::Core)
QT_FEATURE_timezone_tzdb:INTERNAL=OFF
//Qt feature: toolbar (from target Qt6::Widgets)
QT_FEATURE_toolbar:INTERNAL=ON
//Qt feature: toolbox (from target Qt6::Widgets)
QT_FEATURE_toolbox:INTERNAL=ON
//Qt feature: toolbutton (from target Qt6::Widgets)
QT_FEATURE_toolbutton:INTERNAL=ON
//Qt feature: tooltip (from target Qt6::Widgets)
QT_FEATURE_tooltip:INTERNAL=ON
//Qt feature: translation (from target Qt6::Core)
QT_FEATURE_translation:INTERNAL=ON
//Qt feature: transposeproxymodel (from target Qt6::Core)
QT_FEATURE_transposeproxymodel:INTERNAL=ON
//Qt feature: treeview (from target Qt6::Widgets)
QT_FEATURE_treeview:INTERNAL=ON
//Qt feature: treewidget (from target Qt6::Widgets)
QT_FEATURE_treewidget:INTERNAL=ON
//Qt feature: trivial_auto_var_init_pattern (from target Qt6::Core)
QT_FEATURE_trivial_auto_var_init_pattern:INTERNAL=OFF
//Qt feature: tslib (from target Qt6::Gui)
QT_FEATURE_tslib:INTERNAL=OFF
//Qt feature: tuiotouch (from target Qt6::Gui)
QT_FEATURE_tuiotouch:INTERNAL=ON
//Qt feature: undocommand (from target Qt6::Gui)
QT_FEATURE_undocommand:INTERNAL=ON
//Qt feature: undogroup (from target Qt6::Gui)
QT_FEATURE_undogroup:INTERNAL=ON
//Qt feature: undostack (from target Qt6::Gui)
QT_FEATURE_undostack:INTERNAL=ON
//Qt feature: undoview (from target Qt6::Widgets)
QT_FEATURE_undoview:INTERNAL=ON
//Qt feature: use_bfd_linker (from target Qt6::Core)
QT_FEATURE_use_bfd_linker:INTERNAL=OFF
//Qt feature: use_gold_linker (from target Qt6::Core)
QT_FEATURE_use_gold_linker:INTERNAL=OFF
//Qt feature: use_lld_linker (from target Qt6::Core)
QT_FEATURE_use_lld_linker:INTERNAL=OFF
//Qt feature: use_mold_linker (from target Qt6::Core)
QT_FEATURE_use_mold_linker:INTERNAL=OFF
//Qt feature: vaes (from target Qt6::Core)
QT_FEATURE_vaes:INTERNAL=ON
//Qt feature: validator (from target Qt6::Gui)
QT_FEATURE_validator:INTERNAL=ON
//Qt feature: version_tagging (from target Qt6::Core)
QT_FEATURE_version_tagging:INTERNAL=ON
//Qt feature: vkgen (from target Qt6::Gui)
QT_FEATURE_vkgen:INTERNAL=ON
//Qt feature: vkkhrdisplay (from target Qt6::Gui)
QT_FEATURE_vkkhrdisplay:INTERNAL=OFF
//Qt feature: vnc (from target Qt6::Gui)
QT_FEATURE_vnc:INTERNAL=OFF
//Qt feature: vsp2 (from target Qt6::Gui)
QT_FEATURE_vsp2:INTERNAL=OFF
//Qt feature: vulkan (from target Qt6::Gui)
QT_FEATURE_vulkan:INTERNAL=ON
//Qt feature: vxworksevdev (from target Qt6::Gui)
QT_FEATURE_vxworksevdev:INTERNAL=OFF
//Qt feature: wasm_exceptions (from target Qt6::Core)
QT_FEATURE_wasm_exceptions:INTERNAL=OFF
//Qt feature: wasm_jspi (from target Qt6::Core)
QT_FEATURE_wasm_jspi:INTERNAL=OFF
//Qt feature: wasm_simd128 (from target Qt6::Core)
QT_FEATURE_wasm_simd128:INTERNAL=OFF
//Qt feature: wayland (from target Qt6::Gui)
QT_FEATURE_wayland:INTERNAL=OFF
//Qt feature: whatsthis (from target Qt6::Gui)
QT_FEATURE_whatsthis:INTERNAL=ON
//Qt feature: wheelevent (from target Qt6::Gui)
QT_FEATURE_wheelevent:INTERNAL=ON
//Qt feature: widgets (from target Qt6::Core)
QT_FEATURE_widgets:INTERNAL=ON
//Qt feature: widgettextcontrol (from target Qt6::Widgets)
QT_FEATURE_widgettextcontrol:INTERNAL=ON
//Qt feature: wizard (from target Qt6::Widgets)
QT_FEATURE_wizard:INTERNAL=ON
//Qt feature: x86intrin (from target Qt6::Core)
QT_FEATURE_x86intrin:INTERNAL=ON
//Qt feature: xcb (from target Qt6::Gui)
QT_FEATURE_xcb:INTERNAL=OFF
//Qt feature: xcb_egl_plugin (from target Qt6::Gui)
QT_FEATURE_xcb_egl_plugin:INTERNAL=OFF
//Qt feature: xcb_glx (from target Qt6::Gui)
QT_FEATURE_xcb_glx:INTERNAL=OFF
//Qt feature: xcb_glx_plugin (from target Qt6::Gui)
QT_FEATURE_xcb_glx_plugin:INTERNAL=OFF
//Qt feature: xcb_native_painting (from target Qt6::Gui)
QT_FEATURE_xcb_native_painting:INTERNAL=OFF
//Qt feature: xcb_sm (from target Qt6::Gui)
QT_FEATURE_xcb_sm:INTERNAL=OFF
//Qt feature: xcb_xlib (from target Qt6::Gui)
QT_FEATURE_xcb_xlib:INTERNAL=OFF
//Qt feature: xkbcommon (from target Qt6::Gui)
QT_FEATURE_xkbcommon:INTERNAL=OFF
//Qt feature: xkbcommon_x11 (from target Qt6::Gui)
QT_FEATURE_xkbcommon_x11:INTERNAL=OFF
//Qt feature: xlib (from target Qt6::Gui)
QT_FEATURE_xlib:INTERNAL=OFF
//Qt feature: xml (from target Qt6::Core)
QT_FEATURE_xml:INTERNAL=ON
//Qt feature: xmlstream (from target Qt6::Core)
QT_FEATURE_xmlstream:INTERNAL=ON
//Qt feature: xmlstreamreader (from target Qt6::Core)
QT_FEATURE_xmlstreamreader:INTERNAL=ON
//Qt feature: xmlstreamwriter (from target Qt6::Core)
QT_FEATURE_xmlstreamwriter:INTERNAL=ON
//Qt feature: xrender (from target Qt6::Gui)
QT_FEATURE_xrender:INTERNAL=OFF
//Qt feature: zstd (from target Qt6::Core)
QT_FEATURE_zstd:INTERNAL=OFF
//Path to the wrapper of the tool commands
QT_TOOL_COMMAND_WRAPPER_PATH:INTERNAL=C:/Users/<USER>/repos/HVIS/build/.qt/bin/qt_setup_tool_path.bat
//ADVANCED property for variable: Vulkan_GLSLANG_VALIDATOR_EXECUTABLE
Vulkan_GLSLANG_VALIDATOR_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_GLSLC_EXECUTABLE
Vulkan_GLSLC_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_INCLUDE_DIR
Vulkan_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Vulkan_LIBRARY
Vulkan_LIBRARY-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=C:/Program Files/HVIS_Suite
_Qt6_LINGUIST_TOOLS_DIR:INTERNAL=C:/Qt_6/6.9.0/msvc2022_64/lib/cmake/Qt6LinguistTools

